from PyQt6.QtWidgets import (QVBoxLayout, QLabel, QGroupBox, QHBoxLayout,
                             QLineEdit, QPushButton, QComboBox, QTextEdit, QSizePolicy)
from PyQt6.QtGui import QFont  # 添加这行
from modules.base_module import BaseModule
from modules.path_manager import get_path, set_path

class AudioExtractModule(BaseModule):
    def init_ui(self):
        try:
            self.logger.info("初始化音频提取模块")
            
            layout = QVBoxLayout(self)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)
            self.setStyleSheet(self.get_style())
            
            # 标题
            title = QLabel(self.get_title())
            title.setStyleSheet(f"color: {self.get_color()}; font-size: 24px; font-weight: bold;")
            title.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
            layout.addWidget(title)
            
            # 文件选择区域
            file_group = QGroupBox("选择视频文件")
            file_layout = QVBoxLayout(file_group)
            file_layout.setContentsMargins(15, 20, 15, 15)
            
            # 文件路径输入
            path_layout = QHBoxLayout()
            self.path_input = QLineEdit()
            self.path_input.setPlaceholderText("选择视频文件...")
            self.path_input.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            
            browse_btn = QPushButton("浏览...")
            browse_btn.setMinimumSize(100, 40)
            browse_btn.clicked.connect(self.select_file)
            
            path_layout.addWidget(self.path_input)
            path_layout.addWidget(browse_btn)
            file_layout.addLayout(path_layout)
            
            layout.addWidget(file_group)
            
            # 格式选择
            format_group = QGroupBox("音频设置")
            format_layout = QVBoxLayout(format_group)
            format_layout.setContentsMargins(15, 20, 15, 15)
            
            # 输出格式
            format_h_layout = QHBoxLayout()
            format_label = QLabel("音频格式:")
            self.format_combo = QComboBox()
            self.format_combo.addItems(["MP3", "WAV", "AAC", "FLAC"])
            
            format_h_layout.addWidget(format_label)
            format_h_layout.addWidget(self.format_combo)
            format_layout.addLayout(format_h_layout)
            
            layout.addWidget(format_group)
            
            # 操作按钮
            btn_layout = QHBoxLayout()
            btn_layout.addStretch()
            
            self.start_btn = QPushButton("提取音频")
            self.start_btn.setMinimumSize(160, 45)
            self.start_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {self.get_color()};
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #B86ED0;
                }}
            """)
            
            btn_layout.addWidget(self.start_btn)
            layout.addLayout(btn_layout)
            
            # 日志区域
            log_group = QGroupBox("处理日志")
            log_layout = QVBoxLayout(log_group)
            
            self.log_output = QTextEdit()
            self.log_output.setReadOnly(True)
            log_layout.addWidget(self.log_output)
            
            layout.addWidget(log_group, 1)  # 设置拉伸因子
            
            self.logger.info("音频提取模块初始化完成")
        except Exception as e:
            self.logger.error(f"初始化失败: {str(e)}", exc_info=True)
            # 显示错误UI
            layout = QVBoxLayout(self)
            error_label = QLabel(f"初始化错误: {str(e)}")
            error_label.setStyleSheet("color: red; font-size: 16px;")
            layout.addWidget(error_label)
    
    def get_title(self):
        return "音频提取"
    
    def get_color(self):
        return "#C678DD"
    
    def select_file(self):
        """选择视频文件"""
        try:
            from PyQt6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择视频文件",
                get_path('audio_extract_input'),
                "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v *.ts *.mts *.m2ts);;所有文件 (*)"
            )

            if file_path:
                set_path('audio_extract_input', file_path)  # 保存路径
                self.path_input.setText(file_path)
                self.log_output.append(f"> 已选择视频文件: {file_path}")
        except Exception as e:
            self.log_output.append(f"> 选择文件失败: {str(e)}")
