#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文字属性窗口模块
独立的文字属性设置窗口，保持原有的GUI布局和控件配色
"""

import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QSlider,
    QPushButton, QSpinBox, QGroupBox, QColorDialog
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QColor, QFont


class TextPropertiesWindow(QDialog):
    """文字属性设置窗口"""

    # 双信号系统：选中文字和未选中文字的属性信号
    # 字体信号（2条）
    font_changed_unselected = pyqtSignal(str)  # 未选中文字的字体变化
    font_changed_selected = pyqtSignal(str)    # 选中文字的字体变化

    # 样式信号（2条）
    font_style_changed_unselected = pyqtSignal(bool, bool)  # 未选中文字的样式变化(粗体, 斜体)
    font_style_changed_selected = pyqtSignal(bool, bool)    # 选中文字的样式变化(粗体, 斜体)

    # 大小信号（2条）
    font_size_changed_unselected = pyqtSignal(int)  # 未选中文字的大小变化
    font_size_changed_selected = pyqtSignal(int)    # 选中文字的大小变化

    # 填充颜色信号（2条）
    fill_color_changed_unselected = pyqtSignal(QColor)  # 未选中文字的填充颜色变化
    fill_color_changed_selected = pyqtSignal(QColor)    # 选中文字的填充颜色变化

    # 描边颜色信号（2条）
    stroke_color_changed_unselected = pyqtSignal(QColor, int)  # 未选中文字的描边变化(颜色, 宽度)
    stroke_color_changed_selected = pyqtSignal(QColor, int)    # 选中文字的描边变化(颜色, 宽度)

    # 全局属性信号（1条）
    background_changed = pyqtSignal(QColor, int)  # 背景颜色和透明度变化
    spacing_changed = pyqtSignal(int, int)        # 间距变化(字间距, 行距)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("文字属性设置")
        self.setFixedSize(450, 300)  # 固定窗口大小
        self.setModal(False)  # 非模态窗口，可以同时操作主窗口
        
        # 初始化属性值
        self.letter_spacing_value = 0  # 字间距默认0px
        self.line_spacing_value = 100  # 行距默认100%
        self.fill_color = QColor(255, 0, 0)  # 默认红色
        self.stroke_color = QColor(0, 255, 0)  # 默认绿色
        self.bg_color = QColor(0, 0, 255)  # 默认蓝色
        
        self._init_ui()
        self._apply_styles()
        self._setup_connections()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 创建文字属性组框
        text_group = QGroupBox("文字属性")
        text_layout = QVBoxLayout(text_group)
        text_layout.setContentsMargins(10, 15, 10, 10)
        text_layout.setSpacing(8)
        
        # 第一行：字体选择和样式
        self._create_font_row(text_layout)

        # 第二行：字体大小
        self._create_size_row(text_layout)

        # 第三行：间距设置（字间距和行距平行）
        self._create_spacing_row(text_layout)

        # 第四行：填充颜色
        self._create_fill_color_row(text_layout)

        # 第五行：描边设置
        self._create_stroke_row(text_layout)

        # 第六行：背景设置
        self._create_background_row(text_layout)
        
        layout.addWidget(text_group)
        layout.addStretch()  # 添加弹性空间
    
    def _create_font_row(self, layout):
        """创建字体选择行"""
        font_layout = QHBoxLayout()
        
        # 字体标签
        font_label = QLabel("字体:")
        font_label.setFixedWidth(60)
        
        # 字体下拉框
        self.font_combo = QComboBox()
        self.font_combo.setMaximumWidth(120)
        self._load_azt_fonts()
        
        # 字体样式选择
        style_label = QLabel("样式:")
        style_label.setFixedWidth(40)
        self.font_style_combo = QComboBox()
        self.font_style_combo.addItems(["正常", "粗体", "斜体"])
        self.font_style_combo.setFixedWidth(60)

        font_layout.addWidget(font_label)
        font_layout.addWidget(self.font_combo)
        font_layout.addWidget(style_label)
        font_layout.addWidget(self.font_style_combo)
        font_layout.addStretch()
        
        layout.addLayout(font_layout)
    
    def _create_size_row(self, layout):
        """创建字体大小行"""
        size_layout = QHBoxLayout()
        
        size_label = QLabel("大小:")
        size_label.setFixedWidth(60)
        self.font_size_slider = QSlider(Qt.Orientation.Horizontal)
        self.font_size_slider.setRange(12, 200)
        self.font_size_slider.setValue(24)
        self.font_size_value_label = QLabel("24px")
        self.font_size_value_label.setFixedWidth(40)
        
        size_layout.addWidget(size_label)
        size_layout.addWidget(self.font_size_slider, 1)
        size_layout.addWidget(self.font_size_value_label)
        
        layout.addLayout(size_layout)

    def _create_spacing_row(self, layout):
        """创建间距设置行（字间距和行距平行）"""
        spacing_layout = QHBoxLayout()

        # 字间距设置
        letter_spacing_label = QLabel("字间距:")
        letter_spacing_label.setFixedWidth(60)
        self.letter_spacing_slider = QSlider(Qt.Orientation.Horizontal)
        self.letter_spacing_slider.setRange(0, 20)  # 0-20px
        self.letter_spacing_slider.setValue(0)
        self.letter_spacing_slider.setMaximumWidth(120)
        self.letter_spacing_value_label = QLabel("0px")
        self.letter_spacing_value_label.setFixedWidth(35)

        # 行距设置
        line_spacing_label = QLabel("行距:")
        line_spacing_label.setFixedWidth(40)
        self.line_spacing_slider = QSlider(Qt.Orientation.Horizontal)
        self.line_spacing_slider.setRange(50, 200)  # 50%-200%
        self.line_spacing_slider.setValue(100)  # 默认100%
        self.line_spacing_slider.setMaximumWidth(120)
        self.line_spacing_value_label = QLabel("100%")
        self.line_spacing_value_label.setFixedWidth(35)

        spacing_layout.addWidget(letter_spacing_label)
        spacing_layout.addWidget(self.letter_spacing_slider)
        spacing_layout.addWidget(self.letter_spacing_value_label)
        spacing_layout.addWidget(line_spacing_label)
        spacing_layout.addWidget(self.line_spacing_slider)
        spacing_layout.addWidget(self.line_spacing_value_label)
        spacing_layout.addStretch()

        layout.addLayout(spacing_layout)

    def _create_fill_color_row(self, layout):
        """创建填充颜色设置行"""
        fill_layout = QHBoxLayout()

        # 填充颜色
        fill_label = QLabel("填充:")
        fill_label.setFixedWidth(60)
        self.fill_color_btn = QPushButton()
        self.fill_color_btn.setFixedSize(40, 25)
        self.fill_color_btn.setStyleSheet("background-color: red; border: 1px solid #ccc;")

        fill_layout.addWidget(fill_label)
        fill_layout.addWidget(self.fill_color_btn)
        fill_layout.addStretch()

        layout.addLayout(fill_layout)

    def _create_stroke_row(self, layout):
        """创建描边设置行"""
        stroke_layout = QHBoxLayout()

        # 描边颜色
        stroke_label = QLabel("描边:")
        stroke_label.setFixedWidth(60)
        self.stroke_color_btn = QPushButton()
        self.stroke_color_btn.setFixedSize(40, 25)
        self.stroke_color_btn.setStyleSheet("background-color: green; border: 1px solid #ccc;")

        # 描边宽度滑块
        stroke_width_label = QLabel("宽度:")
        stroke_width_label.setFixedWidth(40)
        self.stroke_width_slider = QSlider(Qt.Orientation.Horizontal)
        self.stroke_width_slider.setRange(0, 10)  # 0-10px范围
        self.stroke_width_slider.setValue(0)      # 默认0px（无描边）
        self.stroke_width_slider.setFixedWidth(100)

        # 描边宽度数值显示
        self.stroke_width_label = QLabel("0px")
        self.stroke_width_label.setFixedWidth(30)
        self.stroke_width_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        stroke_layout.addWidget(stroke_label)
        stroke_layout.addWidget(self.stroke_color_btn)
        stroke_layout.addWidget(stroke_width_label)
        stroke_layout.addWidget(self.stroke_width_slider)
        stroke_layout.addWidget(self.stroke_width_label)
        stroke_layout.addStretch()

        layout.addLayout(stroke_layout)
    
    def _create_background_row(self, layout):
        """创建背景设置行"""
        bg_layout = QHBoxLayout()
        
        # 背景颜色
        bg_label = QLabel("背景:")
        bg_label.setFixedWidth(60)
        self.bg_color_btn = QPushButton()
        self.bg_color_btn.setFixedSize(40, 25)
        self.bg_color_btn.setStyleSheet("background-color: blue; border: 1px solid #ccc;")
        
        # 背景透明度
        opacity_label = QLabel("透明度:")
        opacity_label.setFixedWidth(60)
        self.bg_opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.bg_opacity_slider.setRange(0, 100)
        self.bg_opacity_slider.setValue(0)
        self.bg_opacity_value_label = QLabel("0%")
        self.bg_opacity_value_label.setFixedWidth(30)
        
        bg_layout.addWidget(bg_label)
        bg_layout.addWidget(self.bg_color_btn)
        bg_layout.addWidget(opacity_label)
        bg_layout.addWidget(self.bg_opacity_slider, 1)
        bg_layout.addWidget(self.bg_opacity_value_label)
        
        layout.addLayout(bg_layout)
    

    
    def _apply_styles(self):
        """应用样式，保持与原有界面一致的配色"""
        self.setStyleSheet("""
            QDialog {
                background-color: #181A1F;
                color: #D0D0D0;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #5C6370;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                color: #ABB2BF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLabel {
                color: #ABB2BF;
            }
            QComboBox {
                background-color: #2C2E34;
                border: 1px solid #5C6370;
                border-radius: 3px;
                padding: 4px;
                color: #D0D0D0;
            }
            QComboBox:focus {
                border-color: #61AFEF;
            }
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #61AFEF;
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #528BFF;
            }
            QSpinBox {
                background-color: #2C2E34;
                border: 1px solid #5C6370;
                border-radius: 3px;
                padding: 4px;
                color: #D0D0D0;
            }
            QSpinBox:focus {
                border-color: #61AFEF;
            }
        """)
    
    def _load_azt_fonts(self):
        """加载AZT文件夹中的字体"""
        try:
            from PyQt6.QtGui import QFontDatabase

            # 获取项目根目录下的AZT文件夹
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            azt_folder = os.path.join(project_root, "AZT")

            font_list = []
            loaded_fonts = []
            # 创建中文显示名称到实际字体名称的映射
            self.font_name_mapping = {}

            if os.path.exists(azt_folder):
                # 支持的字体文件扩展名
                font_extensions = ['.ttf', '.otf', '.ttc']

                for file in os.listdir(azt_folder):
                    if any(file.lower().endswith(ext) for ext in font_extensions):
                        font_file_path = os.path.join(azt_folder, file)

                        # 动态加载字体文件到应用程序
                        font_id = QFontDatabase.addApplicationFont(font_file_path)

                        if font_id != -1:
                            # 获取字体的实际名称
                            font_families = QFontDatabase.applicationFontFamilies(font_id)
                            if font_families:
                                actual_font_name = font_families[0]

                                # 从文件名提取中文显示名称
                                chinese_name = self._extract_chinese_name(file)

                                # 添加到列表和映射
                                font_list.append(chinese_name)
                                self.font_name_mapping[chinese_name] = actual_font_name
                                loaded_fonts.append((file, chinese_name, actual_font_name))
                                print(f"✅ 成功加载字体: {file} -> 显示名: {chinese_name} -> 实际名: {actual_font_name}")
                            else:
                                print(f"❌ 字体文件无效: {file}")
                        else:
                            print(f"❌ 无法加载字体文件: {file}")

                if font_list:
                    font_list.sort()  # 按字母顺序排序
                    self.font_combo.addItems(font_list)
                    print(f"📝 文字属性窗口成功加载了 {len(font_list)} 个AZT字体")
                    print(f"📝 字体映射: {self.font_name_mapping}")
                else:
                    # 如果AZT文件夹为空，添加系统默认字体
                    default_fonts = ["微软雅黑", "宋体", "黑体", "楷体"]
                    self.font_combo.addItems(default_fonts)
                    # 为系统字体也创建映射（中文名就是实际名）
                    for font in default_fonts:
                        self.font_name_mapping[font] = font
                    print("📝 AZT文件夹为空，使用系统默认字体")
            else:
                # 如果AZT文件夹不存在，添加系统默认字体
                default_fonts = ["微软雅黑", "宋体", "黑体", "楷体"]
                self.font_combo.addItems(default_fonts)
                # 为系统字体也创建映射（中文名就是实际名）
                for font in default_fonts:
                    self.font_name_mapping[font] = font
                print("📝 AZT文件夹不存在，使用系统默认字体")

        except Exception as e:
            print(f"❌ 加载字体失败: {e}")
            import traceback
            traceback.print_exc()
            # 出错时使用默认字体
            default_fonts = ["微软雅黑", "宋体", "黑体", "楷体"]
            self.font_combo.addItems(default_fonts)
            self.font_name_mapping = {font: font for font in default_fonts}

    def _extract_chinese_name(self, filename):
        """从文件名提取中文显示名称"""
        # 去掉扩展名
        name_without_ext = os.path.splitext(filename)[0]

        # 如果文件名以数字开头（如 "01华康布丁体"），去掉数字前缀
        import re
        # 匹配开头的数字和可能的分隔符
        match = re.match(r'^(\d+)[-_\s]*(.+)', name_without_ext)
        if match:
            chinese_name = match.group(2)
        else:
            chinese_name = name_without_ext

        # 清理可能的特殊字符
        chinese_name = chinese_name.strip()

        return chinese_name
    

    

    
    def get_current_properties(self):
        """获取当前所有属性值"""
        return {
            'font_name': self.font_combo.currentText(),
            'font_style': self.font_style_combo.currentText(),
            'font_size': self.font_size_slider.value(),
            'letter_spacing': self.letter_spacing_value,
            'line_spacing': self.line_spacing_value,
            'fill_color': self.fill_color,
            'stroke_color': self.stroke_color,
            'stroke_width': self.stroke_width_slider.value(),
            'bg_color': self.bg_color,
            'bg_opacity': self.bg_opacity_slider.value()
        }
    
    def set_properties(self, properties):
        """设置属性值"""
        if 'font_name' in properties:
            index = self.font_combo.findText(properties['font_name'])
            if index >= 0:
                self.font_combo.setCurrentIndex(index)
        
        if 'font_style' in properties:
            index = self.font_style_combo.findText(properties['font_style'])
            if index >= 0:
                self.font_style_combo.setCurrentIndex(index)
        
        if 'font_size' in properties:
            self.font_size_slider.setValue(properties['font_size'])
        
        if 'letter_spacing' in properties:
            self.letter_spacing_value = properties['letter_spacing']
            self.letter_spacing_slider.setValue(self.letter_spacing_value)

        if 'line_spacing' in properties:
            self.line_spacing_value = properties['line_spacing']
            self.line_spacing_slider.setValue(self.line_spacing_value)
        
        if 'fill_color' in properties:
            self.fill_color = properties['fill_color']
            self.fill_color_btn.setStyleSheet(f"background-color: {self.fill_color.name()}; border: 1px solid #ccc;")
        
        if 'stroke_color' in properties:
            self.stroke_color = properties['stroke_color']
            self.stroke_color_btn.setStyleSheet(f"background-color: {self.stroke_color.name()}; border: 1px solid #ccc;")
        
        if 'stroke_width' in properties:
            width = properties['stroke_width']
            self.stroke_width_slider.setValue(width)
            self.stroke_width_label.setText(f"{width}px")
        
        if 'bg_color' in properties:
            self.bg_color = properties['bg_color']
            self.bg_color_btn.setStyleSheet(f"background-color: {self.bg_color.name()}; border: 1px solid #ccc;")
        
        if 'bg_opacity' in properties:
            self.bg_opacity_slider.setValue(properties['bg_opacity'])

    def _setup_connections(self):
        """设置信号连接"""
        # 字体选择连接
        self.font_combo.currentTextChanged.connect(self._on_font_changed)

        # 字体样式连接
        self.font_style_combo.currentTextChanged.connect(self._on_font_style_changed)

        # 字体大小连接
        self.font_size_slider.valueChanged.connect(self._on_font_size_changed)

        # 填充颜色连接
        self.fill_color_btn.clicked.connect(self._on_fill_color_clicked)

        # 描边颜色连接
        self.stroke_color_btn.clicked.connect(self._on_stroke_color_clicked)
        self.stroke_width_slider.valueChanged.connect(self._on_stroke_width_changed)

        # 背景颜色连接
        self.bg_color_btn.clicked.connect(self._on_bg_color_clicked)
        self.bg_opacity_slider.valueChanged.connect(self._on_bg_opacity_changed)

        # 间距连接
        self.letter_spacing_slider.valueChanged.connect(self._on_letter_spacing_changed)
        self.line_spacing_slider.valueChanged.connect(self._on_line_spacing_changed)

        # 测试信号连接
        print("间距滑块信号连接完成")
        print(f"字间距滑块当前值: {self.letter_spacing_slider.value()}")
        print(f"行距滑块当前值: {self.line_spacing_slider.value()}")

    def _on_font_changed(self, font_name):
        """字体变化事件"""
        print(f"字体变化: {font_name}")

        # 获取实际的字体名称
        actual_font_name = self.get_actual_font_name(font_name)
        print(f"中文显示名: {font_name} -> 实际字体名: {actual_font_name}")

        # 根据是否有选中文字发送不同信号
        if self._has_selected_text():
            print("发送选中文字字体信号")
            self.font_changed_selected.emit(actual_font_name)
        else:
            print("发送未选中文字字体信号")
            self.font_changed_unselected.emit(actual_font_name)

    def get_actual_font_name(self, display_name):
        """根据显示名称获取实际字体名称"""
        if hasattr(self, 'font_name_mapping') and display_name in self.font_name_mapping:
            return self.font_name_mapping[display_name]
        else:
            # 如果映射不存在，直接返回显示名称
            return display_name

    def _on_font_style_changed(self, style_text):
        """字体样式变化事件"""
        bold = "粗体" in style_text
        italic = "斜体" in style_text

        if self._has_selected_text():
            self.font_style_changed_selected.emit(bold, italic)
        else:
            self.font_style_changed_unselected.emit(bold, italic)

    def _on_font_size_changed(self, size):
        """字体大小变化事件"""
        print(f"字体大小变化: {size}")
        if self._has_selected_text():
            print("发送选中文字大小信号")
            self.font_size_changed_selected.emit(size)
        else:
            print("发送未选中文字大小信号")
            self.font_size_changed_unselected.emit(size)

    def _on_fill_color_clicked(self):
        """填充颜色点击事件"""
        color = QColorDialog.getColor(self.fill_color, self, "选择填充颜色")
        if color.isValid():
            self.fill_color = color
            self.fill_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")

            if self._has_selected_text():
                self.fill_color_changed_selected.emit(color)
            else:
                self.fill_color_changed_unselected.emit(color)

    def _on_stroke_color_clicked(self):
        """描边颜色点击事件"""
        color = QColorDialog.getColor(self.stroke_color, self, "选择描边颜色")
        if color.isValid():
            self.stroke_color = color
            self.stroke_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")

            width = self.stroke_width_slider.value()
            if self._has_selected_text():
                self.stroke_color_changed_selected.emit(color, width)
            else:
                self.stroke_color_changed_unselected.emit(color, width)

    def _on_stroke_width_changed(self, width):
        """描边宽度变化事件"""
        # 更新数值显示
        self.stroke_width_label.setText(f"{width}px")

        color = self.stroke_color

        # 0px时表示无描边效果
        if width == 0:
            print(f"描边宽度变化: {width}px (无描边)")
        else:
            print(f"描边宽度变化: {width}px")

        if self._has_selected_text():
            self.stroke_color_changed_selected.emit(color, width)
        else:
            self.stroke_color_changed_unselected.emit(color, width)

    def _on_bg_color_clicked(self):
        """背景颜色点击事件"""
        color = QColorDialog.getColor(self.bg_color, self, "选择背景颜色")
        if color.isValid():
            self.bg_color = color
            self.bg_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")

            opacity = self.bg_opacity_slider.value()
            self.background_changed.emit(color, opacity)

    def _on_bg_opacity_changed(self, opacity):
        """背景透明度变化事件"""
        self.background_changed.emit(self.bg_color, opacity)

    def _on_letter_spacing_changed(self, spacing):
        """字间距变化事件"""
        print(f"字间距滑块变化: {spacing}px")
        self.letter_spacing_value = spacing
        self.letter_spacing_value_label.setText(f"{spacing}px")
        line_spacing = self.line_spacing_value
        self.spacing_changed.emit(spacing, line_spacing)

    def _on_line_spacing_changed(self, spacing):
        """行距变化事件"""
        print(f"行距滑块变化: {spacing}%")
        self.line_spacing_value = spacing
        self.line_spacing_value_label.setText(f"{spacing}%")
        letter_spacing = self.letter_spacing_value
        self.spacing_changed.emit(letter_spacing, spacing)

    def _has_selected_text(self):
        """检查当前是否有选中的文字"""
        # 这个方法需要从父窗口获取当前活动的文本输入框状态
        # 暂时返回False，后续会在封面编辑模块中实现具体逻辑
        if hasattr(self.parent(), 'get_current_text_selection'):
            return self.parent().get_current_text_selection()
        return False
