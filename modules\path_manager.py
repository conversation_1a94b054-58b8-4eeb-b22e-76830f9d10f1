#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局路径管理器
用于管理整个项目的文件路径记忆功能
"""

import os
import json
from pathlib import Path
from typing import Dict, Optional


class PathManager:
    """全局路径管理器"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化路径管理器"""
        if not self._initialized:
            self.paths = {
                # 封面编辑模块
                'cover_edit_video': '',
                'cover_edit_image': '',
                'cover_edit_export': '',
                'cover_edit_watch_folder': '',
                'cover_edit_output_folder': '',
                
                # 视频分割模块
                'video_split_input': '',
                'video_split_output': '',
                
                # 视频转码模块
                'video_transcode_input': '',
                'video_transcode_output': '',
                
                # 音频提取模块
                'audio_extract_input': '',
                'audio_extract_output': '',
            }
            
            self._load_paths()
            PathManager._initialized = True
    
    def _get_config_file(self) -> str:
        """获取配置文件路径"""
        try:
            # 获取当前模块的目录（modules文件夹）
            current_dir = Path(__file__).parent
            # 回到项目根目录
            project_root = current_dir.parent
            # 创建config目录
            config_dir = project_root / 'config'
            
            # 确保目录存在
            config_dir.mkdir(exist_ok=True)
            
            config_file = config_dir / 'global_paths.json'
            return str(config_file)
        except Exception as e:
            print(f"获取配置文件路径失败: {e}")
            return 'global_paths.json'
    
    def _load_paths(self):
        """加载保存的路径"""
        try:
            config_file = self._get_config_file()
            
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_paths = json.load(f)
                
                # 更新路径，保留默认值
                for key, value in saved_paths.items():
                    if key in self.paths:
                        self.paths[key] = value
                
                print(f"📁 全局路径管理器加载完成")
                print(f"📁 配置文件: {config_file}")
            else:
                print("📁 首次使用全局路径管理器，使用默认路径")
                self._set_default_paths()
                
        except Exception as e:
            print(f"加载全局路径配置失败: {e}")
            self._set_default_paths()
    
    def _set_default_paths(self):
        """设置默认路径"""
        try:
            # 默认使用用户的桌面目录
            desktop = Path.home() / 'Desktop'
            if desktop.exists():
                default_path = str(desktop)
            else:
                # 回退到用户主目录
                default_path = str(Path.home())
            
            # 为所有路径设置默认值
            for key in self.paths:
                if not self.paths[key]:  # 只设置空路径
                    self.paths[key] = default_path
            
            print(f"📁 设置默认路径: {default_path}")
            self._save_paths()
            
        except Exception as e:
            print(f"设置默认路径失败: {e}")
    
    def _save_paths(self):
        """保存当前路径"""
        try:
            config_file = self._get_config_file()
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.paths, f, ensure_ascii=False, indent=2)
            
            print(f"💾 全局路径已保存到: {config_file}")
            
        except Exception as e:
            print(f"保存全局路径配置失败: {e}")
    
    def get_path(self, key: str) -> str:
        """获取指定的路径"""
        return self.paths.get(key, '')
    
    def set_path(self, key: str, path: str):
        """设置指定的路径"""
        if key in self.paths:
            # 如果是文件路径，提取目录
            if os.path.isfile(path):
                path = os.path.dirname(path)
            
            self.paths[key] = path
            self._save_paths()
            print(f"📁 更新路径 {key}: {path}")
        else:
            print(f"⚠️ 未知的路径键: {key}")
    
    def get_all_paths(self) -> Dict[str, str]:
        """获取所有路径"""
        return self.paths.copy()
    
    def reset_paths(self):
        """重置所有路径"""
        for key in self.paths:
            self.paths[key] = ''
        self._set_default_paths()
        print("🔄 已重置所有路径")


# 全局路径管理器实例
path_manager = PathManager()


def get_path(key: str) -> str:
    """获取路径的便捷函数"""
    return path_manager.get_path(key)


def set_path(key: str, path: str):
    """设置路径的便捷函数"""
    path_manager.set_path(key, path)


def get_all_paths() -> Dict[str, str]:
    """获取所有路径的便捷函数"""
    return path_manager.get_all_paths()


def reset_paths():
    """重置所有路径的便捷函数"""
    path_manager.reset_paths()
