import os
import sys
import json
from PyQt6.QtWidgets import (QVBoxLayout, QLabel, QGroupBox, QHBoxLayout, 
                             QLineEdit, QPushButton, QComboBox, QTextEdit, 
                             QFileDialog, QSizePolicy, QCheckBox, QProgressBar,
                             QWidget, QSlider, QSpinBox, QTabWidget, QGridLayout,
                             QMessageBox, QFrame, QScrollArea)
from PyQt6.QtGui import QFont, QColor, QLinearGradient, QPainter, QBrush
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QRect, QProcess, QTimer

# 添加modules路径到sys.path以便导入base_module
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from modules.base_module import BaseModule
from modules.path_manager import get_path, set_path
from .processing_engine import ProcessingEngine

class VideoOriginalProcessorModule(BaseModule):
    """视频原创处理模块 - 通过多种技术手段处理视频以避免平台检测"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("视频原创处理")
        self.processing_thread = None
        self.current_processing = False
        self.batch_mode_enabled = False
        
        # 处理参数
        self.processing_params = {
            # 画面处理参数
            'mirror_horizontal': False,
            'mirror_vertical': False,
            'brightness': 0,  # -100 到 100
            'contrast': 0,    # -100 到 100
            'saturation': 0,  # -100 到 100
            'crop_enabled': False,
            'crop_x': 0,
            'crop_y': 0,
            'crop_width': 100,
            'crop_height': 100,
            'watermark_enabled': False,
            'watermark_path': '',
            'watermark_position': 'bottom-right',
            'watermark_opacity': 50,
            
            # 音频处理参数
            'audio_speed': 100,  # 50-200%
            'audio_pitch': 0,    # -12 到 12 半音
            'background_music_enabled': False,
            'background_music_path': '',
            'background_volume': 20,
            'noise_reduction': False,
            
            # 时间轴处理参数
            'video_speed': 100,  # 50-200%
            'random_cuts': False,
            'cuts_count': 3,
            'transition_effects': False,
            'transition_type': 'fade',
            
            # 输出参数
            'output_format': 'mp4',
            'output_quality': 'high',
            'output_resolution': 'original'
        }
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题
        title = QLabel("视频原创处理")
        title.setStyleSheet("""
            QLabel {
                color: #E06C75;
                font-size: 28px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title)
        
        # 说明文本
        desc = QLabel("⚠️ 重要提醒：请确保您有权使用所处理的视频内容，建议优先创作原创内容")
        desc.setStyleSheet("""
            QLabel {
                color: #D19A66;
                font-size: 12px;
                background-color: #2C2416;
                padding: 8px;
                border-radius: 4px;
                border-left: 3px solid #D19A66;
            }
        """)
        desc.setWordWrap(True)
        main_layout.addWidget(desc)
        
        # 文件选择区域
        self.create_file_selection_area(main_layout)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #2C313A;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #E06C75;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #FF7A85;
            }
        """)
        
        # 参数设置区域
        params_widget = QWidget()
        params_layout = QVBoxLayout(params_widget)
        
        # 创建选项卡
        self.create_processing_tabs(params_layout)
        
        scroll_area.setWidget(params_widget)
        main_layout.addWidget(scroll_area)
        
        # 处理控制区域
        self.create_control_area(main_layout)
        
        self.setLayout(main_layout)
    
    def create_file_selection_area(self, parent_layout):
        """创建文件选择区域"""
        group = QGroupBox("文件选择")
        group.setStyleSheet(self.get_group_style())
        layout = QVBoxLayout(group)
        
        # 输入文件
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("输入视频:"))
        
        self.input_path_edit = QLineEdit()
        self.input_path_edit.setPlaceholderText("选择要处理的视频文件...")
        self.input_path_edit.setStyleSheet(self.get_input_style())
        self.input_path_edit.setText(get_path('video_original_input', ''))
        input_layout.addWidget(self.input_path_edit)
        
        input_btn = QPushButton("浏览")
        input_btn.setStyleSheet(self.get_button_style("#E06C75"))
        input_btn.clicked.connect(self.select_input_file)
        input_layout.addWidget(input_btn)
        
        layout.addLayout(input_layout)
        
        # 输出目录
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setPlaceholderText("选择输出目录...")
        self.output_path_edit.setStyleSheet(self.get_input_style())
        self.output_path_edit.setText(get_path('video_original_output', ''))
        output_layout.addWidget(self.output_path_edit)
        
        output_btn = QPushButton("浏览")
        output_btn.setStyleSheet(self.get_button_style("#E06C75"))
        output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(output_btn)
        
        layout.addLayout(output_layout)
        
        parent_layout.addWidget(group)
    
    def create_processing_tabs(self, parent_layout):
        """创建处理参数选项卡"""
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #444;
                background-color: #1E2025;
            }
            QTabBar::tab {
                background-color: #2C313A;
                color: #ABB2BF;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #E06C75;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #3C414A;
            }
        """)
        
        # 画面处理选项卡
        self.create_video_effects_tab(tab_widget)
        
        # 音频处理选项卡
        self.create_audio_effects_tab(tab_widget)
        
        # 时间轴处理选项卡
        self.create_timeline_effects_tab(tab_widget)
        
        # 输出设置选项卡
        self.create_output_settings_tab(tab_widget)
        
        parent_layout.addWidget(tab_widget)
    
    def create_video_effects_tab(self, tab_widget):
        """创建画面处理选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 镜像翻转
        mirror_group = QGroupBox("镜像翻转")
        mirror_group.setStyleSheet(self.get_group_style())
        mirror_layout = QHBoxLayout(mirror_group)
        
        self.mirror_h_check = QCheckBox("水平翻转")
        self.mirror_h_check.setStyleSheet(self.get_checkbox_style())
        mirror_layout.addWidget(self.mirror_h_check)
        
        self.mirror_v_check = QCheckBox("垂直翻转")
        self.mirror_v_check.setStyleSheet(self.get_checkbox_style())
        mirror_layout.addWidget(self.mirror_v_check)
        
        layout.addWidget(mirror_group)
        
        # 色彩调整
        color_group = QGroupBox("色彩调整")
        color_group.setStyleSheet(self.get_group_style())
        color_layout = QGridLayout(color_group)
        
        # 亮度
        color_layout.addWidget(QLabel("亮度:"), 0, 0)
        self.brightness_slider = QSlider(Qt.Orientation.Horizontal)
        self.brightness_slider.setRange(-100, 100)
        self.brightness_slider.setValue(0)
        self.brightness_slider.setStyleSheet(self.get_slider_style())
        color_layout.addWidget(self.brightness_slider, 0, 1)
        self.brightness_label = QLabel("0")
        color_layout.addWidget(self.brightness_label, 0, 2)
        
        # 对比度
        color_layout.addWidget(QLabel("对比度:"), 1, 0)
        self.contrast_slider = QSlider(Qt.Orientation.Horizontal)
        self.contrast_slider.setRange(-100, 100)
        self.contrast_slider.setValue(0)
        self.contrast_slider.setStyleSheet(self.get_slider_style())
        color_layout.addWidget(self.contrast_slider, 1, 1)
        self.contrast_label = QLabel("0")
        color_layout.addWidget(self.contrast_label, 1, 2)
        
        # 饱和度
        color_layout.addWidget(QLabel("饱和度:"), 2, 0)
        self.saturation_slider = QSlider(Qt.Orientation.Horizontal)
        self.saturation_slider.setRange(-100, 100)
        self.saturation_slider.setValue(0)
        self.saturation_slider.setStyleSheet(self.get_slider_style())
        color_layout.addWidget(self.saturation_slider, 2, 1)
        self.saturation_label = QLabel("0")
        color_layout.addWidget(self.saturation_label, 2, 2)
        
        # 连接滑块信号
        self.brightness_slider.valueChanged.connect(lambda v: self.brightness_label.setText(str(v)))
        self.contrast_slider.valueChanged.connect(lambda v: self.contrast_label.setText(str(v)))
        self.saturation_slider.valueChanged.connect(lambda v: self.saturation_label.setText(str(v)))
        
        layout.addWidget(color_group)
        
        tab_widget.addTab(tab, "画面处理")

    def create_audio_effects_tab(self, tab_widget):
        """创建音频处理选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 音频变速
        speed_group = QGroupBox("音频变速")
        speed_group.setStyleSheet(self.get_group_style())
        speed_layout = QHBoxLayout(speed_group)

        speed_layout.addWidget(QLabel("播放速度:"))
        self.audio_speed_slider = QSlider(Qt.Orientation.Horizontal)
        self.audio_speed_slider.setRange(50, 200)
        self.audio_speed_slider.setValue(100)
        self.audio_speed_slider.setStyleSheet(self.get_slider_style())
        speed_layout.addWidget(self.audio_speed_slider)

        self.audio_speed_label = QLabel("100%")
        speed_layout.addWidget(self.audio_speed_label)
        self.audio_speed_slider.valueChanged.connect(lambda v: self.audio_speed_label.setText(f"{v}%"))

        layout.addWidget(speed_group)

        # 音调调整
        pitch_group = QGroupBox("音调调整")
        pitch_group.setStyleSheet(self.get_group_style())
        pitch_layout = QHBoxLayout(pitch_group)

        pitch_layout.addWidget(QLabel("音调偏移:"))
        self.audio_pitch_slider = QSlider(Qt.Orientation.Horizontal)
        self.audio_pitch_slider.setRange(-12, 12)
        self.audio_pitch_slider.setValue(0)
        self.audio_pitch_slider.setStyleSheet(self.get_slider_style())
        pitch_layout.addWidget(self.audio_pitch_slider)

        self.audio_pitch_label = QLabel("0")
        pitch_layout.addWidget(self.audio_pitch_label)
        self.audio_pitch_slider.valueChanged.connect(lambda v: self.audio_pitch_label.setText(f"{v}"))

        layout.addWidget(pitch_group)

        # 背景音乐
        bgm_group = QGroupBox("背景音乐")
        bgm_group.setStyleSheet(self.get_group_style())
        bgm_layout = QVBoxLayout(bgm_group)

        self.bgm_enabled_check = QCheckBox("启用背景音乐")
        self.bgm_enabled_check.setStyleSheet(self.get_checkbox_style())
        bgm_layout.addWidget(self.bgm_enabled_check)

        bgm_file_layout = QHBoxLayout()
        bgm_file_layout.addWidget(QLabel("音乐文件:"))
        self.bgm_path_edit = QLineEdit()
        self.bgm_path_edit.setPlaceholderText("选择背景音乐文件...")
        self.bgm_path_edit.setStyleSheet(self.get_input_style())
        bgm_file_layout.addWidget(self.bgm_path_edit)

        bgm_browse_btn = QPushButton("浏览")
        bgm_browse_btn.setStyleSheet(self.get_button_style("#E06C75"))
        bgm_browse_btn.clicked.connect(self.select_bgm_file)
        bgm_file_layout.addWidget(bgm_browse_btn)
        bgm_layout.addLayout(bgm_file_layout)

        bgm_volume_layout = QHBoxLayout()
        bgm_volume_layout.addWidget(QLabel("音量:"))
        self.bgm_volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.bgm_volume_slider.setRange(0, 100)
        self.bgm_volume_slider.setValue(20)
        self.bgm_volume_slider.setStyleSheet(self.get_slider_style())
        bgm_volume_layout.addWidget(self.bgm_volume_slider)

        self.bgm_volume_label = QLabel("20%")
        bgm_volume_layout.addWidget(self.bgm_volume_label)
        self.bgm_volume_slider.valueChanged.connect(lambda v: self.bgm_volume_label.setText(f"{v}%"))
        bgm_layout.addLayout(bgm_volume_layout)

        layout.addWidget(bgm_group)

        # 降噪处理
        noise_group = QGroupBox("降噪处理")
        noise_group.setStyleSheet(self.get_group_style())
        noise_layout = QHBoxLayout(noise_group)

        self.noise_reduction_check = QCheckBox("启用音频降噪")
        self.noise_reduction_check.setStyleSheet(self.get_checkbox_style())
        noise_layout.addWidget(self.noise_reduction_check)

        layout.addWidget(noise_group)

        tab_widget.addTab(tab, "音频处理")

    def create_timeline_effects_tab(self, tab_widget):
        """创建时间轴处理选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 视频变速
        video_speed_group = QGroupBox("视频变速")
        video_speed_group.setStyleSheet(self.get_group_style())
        video_speed_layout = QHBoxLayout(video_speed_group)

        video_speed_layout.addWidget(QLabel("播放速度:"))
        self.video_speed_slider = QSlider(Qt.Orientation.Horizontal)
        self.video_speed_slider.setRange(50, 200)
        self.video_speed_slider.setValue(100)
        self.video_speed_slider.setStyleSheet(self.get_slider_style())
        video_speed_layout.addWidget(self.video_speed_slider)

        self.video_speed_label = QLabel("100%")
        video_speed_layout.addWidget(self.video_speed_label)
        self.video_speed_slider.valueChanged.connect(lambda v: self.video_speed_label.setText(f"{v}%"))

        layout.addWidget(video_speed_group)

        # 随机剪切
        cuts_group = QGroupBox("随机剪切")
        cuts_group.setStyleSheet(self.get_group_style())
        cuts_layout = QVBoxLayout(cuts_group)

        self.random_cuts_check = QCheckBox("启用随机剪切")
        self.random_cuts_check.setStyleSheet(self.get_checkbox_style())
        cuts_layout.addWidget(self.random_cuts_check)

        cuts_count_layout = QHBoxLayout()
        cuts_count_layout.addWidget(QLabel("剪切次数:"))
        self.cuts_count_spin = QSpinBox()
        self.cuts_count_spin.setRange(1, 10)
        self.cuts_count_spin.setValue(3)
        self.cuts_count_spin.setStyleSheet(self.get_spinbox_style())
        cuts_count_layout.addWidget(self.cuts_count_spin)
        cuts_layout.addLayout(cuts_count_layout)

        layout.addWidget(cuts_group)

        # 转场效果
        transition_group = QGroupBox("转场效果")
        transition_group.setStyleSheet(self.get_group_style())
        transition_layout = QVBoxLayout(transition_group)

        self.transition_enabled_check = QCheckBox("启用转场效果")
        self.transition_enabled_check.setStyleSheet(self.get_checkbox_style())
        transition_layout.addWidget(self.transition_enabled_check)

        transition_type_layout = QHBoxLayout()
        transition_type_layout.addWidget(QLabel("转场类型:"))
        self.transition_type_combo = QComboBox()
        self.transition_type_combo.addItems(["淡入淡出", "滑动", "缩放", "旋转"])
        self.transition_type_combo.setStyleSheet(self.get_combo_style())
        transition_type_layout.addWidget(self.transition_type_combo)
        transition_layout.addLayout(transition_type_layout)

        layout.addWidget(transition_group)

        tab_widget.addTab(tab, "时间轴处理")

    def create_output_settings_tab(self, tab_widget):
        """创建输出设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 输出格式
        format_group = QGroupBox("输出格式")
        format_group.setStyleSheet(self.get_group_style())
        format_layout = QHBoxLayout(format_group)

        format_layout.addWidget(QLabel("格式:"))
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["mp4", "avi", "mov", "mkv"])
        self.output_format_combo.setStyleSheet(self.get_combo_style())
        format_layout.addWidget(self.output_format_combo)

        layout.addWidget(format_group)

        # 输出质量
        quality_group = QGroupBox("输出质量")
        quality_group.setStyleSheet(self.get_group_style())
        quality_layout = QHBoxLayout(quality_group)

        quality_layout.addWidget(QLabel("质量:"))
        self.output_quality_combo = QComboBox()
        self.output_quality_combo.addItems(["低质量", "中等质量", "高质量", "超高质量"])
        self.output_quality_combo.setCurrentText("高质量")
        self.output_quality_combo.setStyleSheet(self.get_combo_style())
        quality_layout.addWidget(self.output_quality_combo)

        layout.addWidget(quality_group)

        # 输出分辨率
        resolution_group = QGroupBox("输出分辨率")
        resolution_group.setStyleSheet(self.get_group_style())
        resolution_layout = QHBoxLayout(resolution_group)

        resolution_layout.addWidget(QLabel("分辨率:"))
        self.output_resolution_combo = QComboBox()
        self.output_resolution_combo.addItems(["保持原始", "720p", "1080p", "1440p", "4K"])
        self.output_resolution_combo.setStyleSheet(self.get_combo_style())
        resolution_layout.addWidget(self.output_resolution_combo)

        layout.addWidget(resolution_group)

        tab_widget.addTab(tab, "输出设置")

    def create_control_area(self, parent_layout):
        """创建处理控制区域"""
        control_frame = QFrame()
        control_frame.setStyleSheet("""
            QFrame {
                background-color: #24262B;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        control_layout = QVBoxLayout(control_frame)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: #1E2025;
                border: 1px solid #444;
                border-radius: 5px;
                text-align: center;
                height: 25px;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #E06C75;
                border-radius: 4px;
            }
        """)
        control_layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #ABB2BF;
                font-size: 14px;
                padding: 5px;
            }
        """)
        control_layout.addWidget(self.status_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 预设按钮
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("快速预设:"))

        light_preset_btn = QPushButton("轻度处理")
        light_preset_btn.setStyleSheet(self.get_button_style("#98C379"))
        light_preset_btn.clicked.connect(self.apply_light_preset)
        preset_layout.addWidget(light_preset_btn)

        medium_preset_btn = QPushButton("中度处理")
        medium_preset_btn.setStyleSheet(self.get_button_style("#D19A66"))
        medium_preset_btn.clicked.connect(self.apply_medium_preset)
        preset_layout.addWidget(medium_preset_btn)

        heavy_preset_btn = QPushButton("重度处理")
        heavy_preset_btn.setStyleSheet(self.get_button_style("#E06C75"))
        heavy_preset_btn.clicked.connect(self.apply_heavy_preset)
        preset_layout.addWidget(heavy_preset_btn)

        button_layout.addLayout(preset_layout)
        button_layout.addStretch()

        # 主要控制按钮
        self.start_btn = QPushButton("开始处理")
        self.start_btn.setStyleSheet(self.get_button_style("#E06C75", size="large"))
        self.start_btn.clicked.connect(self.start_processing)
        button_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.setStyleSheet(self.get_button_style("#E06C75", size="large"))
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        control_layout.addLayout(button_layout)
        parent_layout.addWidget(control_frame)

    def select_input_file(self):
        """选择输入视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            get_path('video_original_input', ''),
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv *.wmv);;所有文件 (*)"
        )
        if file_path:
            self.input_path_edit.setText(file_path)
            set_path('video_original_input', file_path)

    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            get_path('video_original_output', '')
        )
        if dir_path:
            self.output_path_edit.setText(dir_path)
            set_path('video_original_output', dir_path)

    def select_bgm_file(self):
        """选择背景音乐文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择音频文件",
            "",
            "音频文件 (*.mp3 *.wav *.aac *.flac *.ogg);;所有文件 (*)"
        )
        if file_path:
            self.bgm_path_edit.setText(file_path)

    def apply_light_preset(self):
        """应用轻度处理预设"""
        # 轻微的色彩调整
        self.brightness_slider.setValue(5)
        self.contrast_slider.setValue(3)
        self.saturation_slider.setValue(2)

        # 轻微的音频调整
        self.audio_speed_slider.setValue(102)
        self.audio_pitch_slider.setValue(1)

        # 轻微的视频调整
        self.video_speed_slider.setValue(101)

        self.status_label.setText("已应用轻度处理预设")

    def apply_medium_preset(self):
        """应用中度处理预设"""
        # 中等的色彩调整
        self.brightness_slider.setValue(10)
        self.contrast_slider.setValue(8)
        self.saturation_slider.setValue(5)

        # 启用镜像翻转
        self.mirror_h_check.setChecked(True)

        # 中等的音频调整
        self.audio_speed_slider.setValue(105)
        self.audio_pitch_slider.setValue(2)

        # 中等的视频调整
        self.video_speed_slider.setValue(103)

        # 启用随机剪切
        self.random_cuts_check.setChecked(True)
        self.cuts_count_spin.setValue(2)

        self.status_label.setText("已应用中度处理预设")

    def apply_heavy_preset(self):
        """应用重度处理预设"""
        # 较大的色彩调整
        self.brightness_slider.setValue(15)
        self.contrast_slider.setValue(12)
        self.saturation_slider.setValue(10)

        # 启用镜像翻转
        self.mirror_h_check.setChecked(True)
        self.mirror_v_check.setChecked(True)

        # 较大的音频调整
        self.audio_speed_slider.setValue(110)
        self.audio_pitch_slider.setValue(3)

        # 较大的视频调整
        self.video_speed_slider.setValue(107)

        # 启用随机剪切和转场
        self.random_cuts_check.setChecked(True)
        self.cuts_count_spin.setValue(5)
        self.transition_enabled_check.setChecked(True)

        # 启用降噪
        self.noise_reduction_check.setChecked(True)

        self.status_label.setText("已应用重度处理预设")

    def start_processing(self):
        """开始处理视频"""
        # 验证输入
        if not self.input_path_edit.text().strip():
            QMessageBox.warning(self, "警告", "请选择输入视频文件")
            return

        if not self.output_path_edit.text().strip():
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return

        if not os.path.exists(self.input_path_edit.text()):
            QMessageBox.warning(self, "警告", "输入文件不存在")
            return

        if not os.path.exists(self.output_path_edit.text()):
            QMessageBox.warning(self, "警告", "输出目录不存在")
            return

        # 收集处理参数
        params = self.collect_processing_params()

        # 创建处理线程
        from .processing_engine import ProcessingEngine
        self.processing_thread = ProcessingEngine(
            self.input_path_edit.text(),
            self.output_path_edit.text(),
            params
        )

        # 连接信号
        self.processing_thread.progress_updated.connect(self.progress_bar.setValue)
        self.processing_thread.status_updated.connect(self.status_label.setText)
        self.processing_thread.processing_finished.connect(self.on_processing_finished)

        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.current_processing = True

        # 开始处理
        self.processing_thread.start()

    def stop_processing(self):
        """停止处理"""
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.cancel()
            self.processing_thread.wait(3000)  # 等待3秒

        self.on_processing_finished(False, "处理已停止")

    def on_processing_finished(self, success, message):
        """处理完成回调"""
        self.current_processing = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.warning(self, "失败", message)

        self.progress_bar.setValue(0)

    def collect_processing_params(self):
        """收集所有处理参数"""
        params = {
            # 画面处理参数
            'mirror_horizontal': self.mirror_h_check.isChecked(),
            'mirror_vertical': self.mirror_v_check.isChecked(),
            'brightness': self.brightness_slider.value(),
            'contrast': self.contrast_slider.value(),
            'saturation': self.saturation_slider.value(),

            # 音频处理参数
            'audio_speed': self.audio_speed_slider.value(),
            'audio_pitch': self.audio_pitch_slider.value(),
            'background_music_enabled': self.bgm_enabled_check.isChecked(),
            'background_music_path': self.bgm_path_edit.text(),
            'background_volume': self.bgm_volume_slider.value(),
            'noise_reduction': self.noise_reduction_check.isChecked(),

            # 时间轴处理参数
            'video_speed': self.video_speed_slider.value(),
            'random_cuts': self.random_cuts_check.isChecked(),
            'cuts_count': self.cuts_count_spin.value(),
            'transition_effects': self.transition_enabled_check.isChecked(),
            'transition_type': self.transition_type_combo.currentText(),

            # 输出参数
            'output_format': self.output_format_combo.currentText(),
            'output_quality': self.output_quality_combo.currentText(),
            'output_resolution': self.output_resolution_combo.currentText()
        }

        return params

    # 样式方法
    def get_group_style(self):
        return """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #444;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #1E2025;
                color: #ABB2BF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #E06C75;
            }
        """

    def get_input_style(self):
        return """
            QLineEdit {
                background-color: #2C313A;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 8px;
                color: #ABB2BF;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #E06C75;
            }
        """

    def get_button_style(self, color, size="normal"):
        if size == "large":
            return f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 12px 24px;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 120px;
                }}
                QPushButton:hover {{
                    background-color: {self.lighten_color(color)};
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color)};
                }}
                QPushButton:disabled {{
                    background-color: #444;
                    color: #888;
                }}
            """
        else:
            return f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {self.lighten_color(color)};
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color)};
                }}
            """

    def get_checkbox_style(self):
        return """
            QCheckBox {
                color: #ABB2BF;
                font-size: 14px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #444;
                background-color: #2C313A;
            }
            QCheckBox::indicator:checked {
                background-color: #E06C75;
                border-color: #E06C75;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #FF7A85;
            }
        """

    def get_slider_style(self):
        return """
            QSlider::groove:horizontal {
                border: 1px solid #444;
                height: 8px;
                background: #2C313A;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #E06C75;
                border: 1px solid #E06C75;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #FF7A85;
            }
        """

    def get_combo_style(self):
        return """
            QComboBox {
                background-color: #2C313A;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 8px;
                color: #ABB2BF;
                font-size: 14px;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #E06C75;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #ABB2BF;
            }
            QComboBox QAbstractItemView {
                background-color: #2C313A;
                border: 1px solid #444;
                selection-background-color: #E06C75;
                color: #ABB2BF;
            }
        """

    def get_spinbox_style(self):
        return """
            QSpinBox {
                background-color: #2C313A;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 8px;
                color: #ABB2BF;
                font-size: 14px;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #E06C75;
            }
        """

    def lighten_color(self, color):
        """使颜色变亮"""
        color_map = {
            '#E06C75': '#FF7A85',
            '#98C379': '#A8D389',
            '#D19A66': '#E1AA76'
        }
        return color_map.get(color, color)

    def darken_color(self, color):
        """使颜色变暗"""
        color_map = {
            '#E06C75': '#D05C65',
            '#98C379': '#88B369',
            '#D19A66': '#C18A56'
        }
        return color_map.get(color, color)
