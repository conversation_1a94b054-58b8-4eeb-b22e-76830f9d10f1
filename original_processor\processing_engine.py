import os
import subprocess
import random
import json
import tempfile
from PyQt6.QtCore import QThread, pyqtSignal

class ProcessingEngine(QThread):
    """视频处理引擎 - 负责执行实际的视频处理操作"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    processing_finished = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, input_path, output_path, params):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path
        self.params = params
        self.is_cancelled = False
        
        # FFmpeg路径
        self.ffmpeg_path = self.get_ffmpeg_path()
    
    def get_ffmpeg_path(self):
        """获取FFmpeg可执行文件路径"""
        # 首先检查项目bin目录
        project_ffmpeg = os.path.join(os.path.dirname(__file__), '..', 'bin', 'ffmpeg.exe')
        if os.path.exists(project_ffmpeg):
            return project_ffmpeg
        
        # 检查系统PATH
        try:
            result = subprocess.run(['where', 'ffmpeg'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                return result.stdout.strip().split('\n')[0]
        except:
            pass
        
        # 默认假设在PATH中
        return 'ffmpeg'
    
    def run(self):
        """执行视频处理"""
        try:
            self.status_updated.emit("开始处理视频...")
            self.progress_updated.emit(0)
            
            # 生成输出文件名
            input_name = os.path.splitext(os.path.basename(self.input_path))[0]
            output_format = self.params.get('output_format', 'mp4')
            output_filename = f"{input_name}_processed.{output_format}"
            final_output_path = os.path.join(self.output_path, output_filename)
            
            # 构建FFmpeg命令
            ffmpeg_cmd = self.build_ffmpeg_command(final_output_path)
            
            if self.is_cancelled:
                return
            
            self.status_updated.emit("正在处理视频...")
            self.progress_updated.emit(10)
            
            # 执行FFmpeg命令
            success = self.execute_ffmpeg_command(ffmpeg_cmd)
            
            if success and not self.is_cancelled:
                self.progress_updated.emit(100)
                self.status_updated.emit("处理完成")
                self.processing_finished.emit(True, f"视频已保存到: {final_output_path}")
            elif self.is_cancelled:
                self.status_updated.emit("处理已取消")
                self.processing_finished.emit(False, "处理被用户取消")
            else:
                self.status_updated.emit("处理失败")
                self.processing_finished.emit(False, "视频处理失败，请检查输入文件和参数")
                
        except Exception as e:
            self.status_updated.emit("处理出错")
            self.processing_finished.emit(False, f"处理过程中出现错误: {str(e)}")
    
    def build_ffmpeg_command(self, output_path):
        """构建FFmpeg命令"""
        cmd = [self.ffmpeg_path, '-i', self.input_path]
        
        # 视频滤镜
        video_filters = []
        
        # 镜像翻转
        if self.params.get('mirror_horizontal', False):
            video_filters.append('hflip')
        if self.params.get('mirror_vertical', False):
            video_filters.append('vflip')
        
        # 色彩调整
        brightness = self.params.get('brightness', 0)
        contrast = self.params.get('contrast', 0)
        saturation = self.params.get('saturation', 0)
        
        if brightness != 0 or contrast != 0 or saturation != 0:
            # 将参数转换为FFmpeg格式
            brightness_val = brightness / 100.0  # -1.0 到 1.0
            contrast_val = 1.0 + (contrast / 100.0)  # 0.0 到 2.0
            saturation_val = 1.0 + (saturation / 100.0)  # 0.0 到 2.0
            
            eq_filter = f"eq=brightness={brightness_val}:contrast={contrast_val}:saturation={saturation_val}"
            video_filters.append(eq_filter)
        
        # 视频变速
        video_speed = self.params.get('video_speed', 100)
        if video_speed != 100:
            speed_factor = video_speed / 100.0
            video_filters.append(f"setpts={1/speed_factor}*PTS")
        
        # 应用视频滤镜
        if video_filters:
            cmd.extend(['-vf', ','.join(video_filters)])
        
        # 音频处理
        audio_filters = []
        
        # 音频变速
        audio_speed = self.params.get('audio_speed', 100)
        if audio_speed != 100:
            speed_factor = audio_speed / 100.0
            audio_filters.append(f"atempo={speed_factor}")
        
        # 音调调整
        audio_pitch = self.params.get('audio_pitch', 0)
        if audio_pitch != 0:
            # 使用asetrate和atempo来改变音调
            pitch_factor = 2 ** (audio_pitch / 12.0)
            audio_filters.append(f"asetrate=44100*{pitch_factor},atempo={1/pitch_factor}")
        
        # 降噪
        if self.params.get('noise_reduction', False):
            audio_filters.append('afftdn')
        
        # 应用音频滤镜
        if audio_filters:
            cmd.extend(['-af', ','.join(audio_filters)])
        
        # 输出质量设置
        quality = self.params.get('output_quality', 'high')
        if quality == '低质量':
            cmd.extend(['-crf', '28'])
        elif quality == '中等质量':
            cmd.extend(['-crf', '23'])
        elif quality == '高质量':
            cmd.extend(['-crf', '18'])
        elif quality == '超高质量':
            cmd.extend(['-crf', '15'])
        
        # 输出分辨率
        resolution = self.params.get('output_resolution', 'original')
        if resolution != '保持原始':
            if resolution == '720p':
                cmd.extend(['-s', '1280x720'])
            elif resolution == '1080p':
                cmd.extend(['-s', '1920x1080'])
            elif resolution == '1440p':
                cmd.extend(['-s', '2560x1440'])
            elif resolution == '4K':
                cmd.extend(['-s', '3840x2160'])
        
        # 编码器设置
        cmd.extend(['-c:v', 'libx264', '-c:a', 'aac'])
        
        # 覆盖输出文件
        cmd.extend(['-y', output_path])
        
        return cmd
    
    def execute_ffmpeg_command(self, cmd):
        """执行FFmpeg命令"""
        try:
            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            # 模拟进度更新
            progress = 10
            while process.poll() is None:
                if self.is_cancelled:
                    process.terminate()
                    return False
                
                # 更新进度
                if progress < 90:
                    progress += random.randint(1, 5)
                    self.progress_updated.emit(min(progress, 90))
                
                # 等待一小段时间
                self.msleep(500)
            
            # 检查返回码
            return process.returncode == 0
            
        except Exception as e:
            print(f"FFmpeg执行错误: {e}")
            return False
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
    
    def apply_random_cuts(self, input_path, output_path):
        """应用随机剪切效果"""
        # 这里可以实现更复杂的随机剪切逻辑
        # 目前简化处理
        pass
    
    def add_transition_effects(self, input_path, output_path):
        """添加转场效果"""
        # 这里可以实现转场效果
        # 目前简化处理
        pass
    
    def add_watermark(self, input_path, output_path, watermark_path):
        """添加水印"""
        # 这里可以实现水印添加功能
        # 目前简化处理
        pass
