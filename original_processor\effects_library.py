import random
import math

class EffectsLibrary:
    """视频效果库 - 提供各种视频处理效果的参数和算法"""
    
    @staticmethod
    def get_random_color_adjustment():
        """获取随机色彩调整参数"""
        return {
            'brightness': random.randint(-15, 15),
            'contrast': random.randint(-10, 15),
            'saturation': random.randint(-8, 12),
            'hue': random.randint(-5, 5)
        }
    
    @staticmethod
    def get_random_speed_adjustment():
        """获取随机速度调整参数"""
        # 避免过于极端的速度变化
        speed_options = [95, 96, 97, 98, 99, 101, 102, 103, 104, 105, 106, 107, 108]
        return {
            'video_speed': random.choice(speed_options),
            'audio_speed': random.choice(speed_options)
        }
    
    @staticmethod
    def get_random_audio_adjustment():
        """获取随机音频调整参数"""
        return {
            'pitch': random.randint(-2, 2),
            'volume': random.randint(95, 105),
            'bass_boost': random.choice([True, False]),
            'treble_boost': random.choice([True, False])
        }
    
    @staticmethod
    def get_stealth_processing_preset():
        """获取隐蔽处理预设 - 微小但有效的改动"""
        return {
            # 画面处理
            'mirror_horizontal': random.choice([True, False]),
            'brightness': random.randint(-5, 5),
            'contrast': random.randint(-3, 5),
            'saturation': random.randint(-2, 3),
            
            # 音频处理
            'audio_speed': random.randint(98, 102),
            'audio_pitch': random.randint(-1, 1),
            
            # 时间轴处理
            'video_speed': random.randint(99, 101),
            
            # 随机微调
            'add_noise': True,
            'noise_level': random.randint(1, 3)
        }
    
    @staticmethod
    def get_aggressive_processing_preset():
        """获取激进处理预设 - 较大改动但保持观看体验"""
        return {
            # 画面处理
            'mirror_horizontal': True,
            'mirror_vertical': random.choice([True, False]),
            'brightness': random.randint(-20, 20),
            'contrast': random.randint(-15, 20),
            'saturation': random.randint(-10, 15),
            
            # 音频处理
            'audio_speed': random.randint(95, 110),
            'audio_pitch': random.randint(-3, 3),
            'noise_reduction': True,
            
            # 时间轴处理
            'video_speed': random.randint(95, 108),
            'random_cuts': True,
            'cuts_count': random.randint(2, 5),
            'transition_effects': True,
            
            # 额外效果
            'add_watermark': random.choice([True, False]),
            'crop_enabled': random.choice([True, False])
        }
    
    @staticmethod
    def get_platform_specific_preset(platform='kuaishou'):
        """获取平台特定的处理预设"""
        presets = {
            'kuaishou': {
                # 快手平台优化
                'mirror_horizontal': True,
                'brightness': random.randint(5, 15),
                'contrast': random.randint(3, 10),
                'saturation': random.randint(2, 8),
                'audio_speed': random.randint(102, 108),
                'video_speed': random.randint(101, 105),
                'add_logo': True,
                'logo_position': 'bottom-right',
                'logo_opacity': random.randint(30, 60)
            },
            'douyin': {
                # 抖音平台优化
                'mirror_horizontal': random.choice([True, False]),
                'brightness': random.randint(-10, 10),
                'contrast': random.randint(-5, 15),
                'saturation': random.randint(-5, 10),
                'audio_pitch': random.randint(-2, 2),
                'video_speed': random.randint(98, 103),
                'transition_effects': True,
                'transition_type': 'fade'
            },
            'bilibili': {
                # B站平台优化
                'brightness': random.randint(-8, 12),
                'contrast': random.randint(-3, 8),
                'saturation': random.randint(-3, 6),
                'audio_speed': random.randint(99, 104),
                'video_speed': random.randint(100, 102),
                'noise_reduction': True,
                'quality_enhance': True
            }
        }
        
        return presets.get(platform, presets['kuaishou'])
    
    @staticmethod
    def generate_random_cuts_timeline(duration_seconds, cuts_count=3):
        """生成随机剪切时间轴"""
        if duration_seconds < 10 or cuts_count < 1:
            return []
        
        # 确保不在开头和结尾剪切
        safe_start = 2
        safe_end = duration_seconds - 2
        safe_duration = safe_end - safe_start
        
        if safe_duration < cuts_count:
            cuts_count = max(1, int(safe_duration))
        
        # 生成随机剪切点
        cut_points = []
        for _ in range(cuts_count):
            cut_time = safe_start + random.uniform(0, safe_duration)
            cut_duration = random.uniform(0.5, 2.0)  # 剪切0.5-2秒
            cut_points.append({
                'start': cut_time,
                'duration': cut_duration
            })
        
        # 按时间排序
        cut_points.sort(key=lambda x: x['start'])
        
        return cut_points
    
    @staticmethod
    def get_transition_effects():
        """获取可用的转场效果列表"""
        return {
            'fade': {
                'name': '淡入淡出',
                'ffmpeg_filter': 'fade',
                'duration': 0.5
            },
            'slide_left': {
                'name': '左滑',
                'ffmpeg_filter': 'slide=direction=left',
                'duration': 0.3
            },
            'slide_right': {
                'name': '右滑',
                'ffmpeg_filter': 'slide=direction=right',
                'duration': 0.3
            },
            'zoom_in': {
                'name': '放大',
                'ffmpeg_filter': 'zoompan=z=\'zoom+0.002\':d=25',
                'duration': 1.0
            },
            'zoom_out': {
                'name': '缩小',
                'ffmpeg_filter': 'zoompan=z=\'if(lte(zoom,1.0),1.5,max(1.001,zoom-0.002))\':d=25',
                'duration': 1.0
            }
        }
    
    @staticmethod
    def calculate_optimal_bitrate(resolution, quality='high'):
        """计算最优比特率"""
        # 基础比特率表 (kbps)
        base_bitrates = {
            '720p': {'low': 1500, 'medium': 2500, 'high': 4000, 'ultra': 6000},
            '1080p': {'low': 3000, 'medium': 5000, 'high': 8000, 'ultra': 12000},
            '1440p': {'low': 6000, 'medium': 10000, 'high': 16000, 'ultra': 24000},
            '4K': {'low': 15000, 'medium': 25000, 'high': 40000, 'ultra': 60000}
        }
        
        quality_map = {
            '低质量': 'low',
            '中等质量': 'medium', 
            '高质量': 'high',
            '超高质量': 'ultra'
        }
        
        quality_key = quality_map.get(quality, 'high')
        
        if resolution in base_bitrates:
            return base_bitrates[resolution][quality_key]
        else:
            # 默认1080p高质量
            return base_bitrates['1080p'][quality_key]
    
    @staticmethod
    def get_noise_patterns():
        """获取噪声模式用于增加随机性"""
        return {
            'film_grain': {
                'name': '胶片颗粒',
                'ffmpeg_filter': 'noise=alls=20:allf=t+u',
                'strength': 'light'
            },
            'digital_noise': {
                'name': '数字噪声',
                'ffmpeg_filter': 'noise=c0s=10:c0f=u',
                'strength': 'medium'
            },
            'vintage_effect': {
                'name': '复古效果',
                'ffmpeg_filter': 'curves=vintage',
                'strength': 'heavy'
            }
        }
    
    @staticmethod
    def generate_processing_report(params):
        """生成处理报告"""
        report = {
            'timestamp': None,
            'input_file': params.get('input_path', ''),
            'output_file': params.get('output_path', ''),
            'processing_effects': [],
            'estimated_detection_resistance': 'medium'
        }
        
        # 分析应用的效果
        effects_count = 0
        
        if params.get('mirror_horizontal') or params.get('mirror_vertical'):
            report['processing_effects'].append('镜像翻转')
            effects_count += 2
        
        if any([params.get('brightness', 0) != 0, params.get('contrast', 0) != 0, params.get('saturation', 0) != 0]):
            report['processing_effects'].append('色彩调整')
            effects_count += 1
        
        if params.get('audio_speed', 100) != 100 or params.get('audio_pitch', 0) != 0:
            report['processing_effects'].append('音频处理')
            effects_count += 1
        
        if params.get('video_speed', 100) != 100:
            report['processing_effects'].append('视频变速')
            effects_count += 1
        
        if params.get('random_cuts', False):
            report['processing_effects'].append('随机剪切')
            effects_count += 2
        
        # 评估检测抗性
        if effects_count >= 5:
            report['estimated_detection_resistance'] = 'high'
        elif effects_count >= 3:
            report['estimated_detection_resistance'] = 'medium'
        else:
            report['estimated_detection_resistance'] = 'low'
        
        return report
