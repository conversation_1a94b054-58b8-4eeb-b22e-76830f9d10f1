import os
import cv2
import subprocess
import tempfile
from typing import Optional, Dict, Any, List, Tuple
from PIL import Image, ImageDraw, ImageFont
import numpy as np

from PyQt6.QtWidgets import (
    QWidget, QLabel, QVBoxLayout, QPushButton, QHBoxLayout,
    QFileDialog, QLineEdit, QGroupBox, QMessageBox, QSizePolicy,
    QStackedWidget, QSlider, QButtonGroup, QSplitter,
    QGridLayout, QCheckBox, QTextEdit, QProgressBar, QComboBox, QSpinBox
)
from PyQt6.QtGui import (
    QPixmap, QImage, QPainter, QColor, QFont, QTextOption
)
from PyQt6.QtCore import Qt, pyqtSignal, QUrl, QThread, QTimer, QFileSystemWatcher

# 添加多媒体组件导入
from PyQt6.QtMultimediaWidgets import QVideoWidget
from PyQt6.QtMultimedia import QMediaPlayer, QAudioOutput

# 导入自定义模块
# from mk.text_editor_simple import TextEditorSimple as TextEditor  # 功能已移除
from mk.export_settings import VideoExportSettings
from modules.base_module import BaseModule
from modules.path_manager import get_path, set_path








# 常量定义
class Constants:
    """集中管理常量"""
    MSG_ERROR = "错误"
    MSG_WARNING = "警告"
    MSG_SUCCESS = "成功"
    
    # 默认尺寸
    MIN_PREVIEW_SIZE = (300, 200)
    DEFAULT_VIDEO_SIZE = (1920, 1080)
    DEFAULT_ASPECT_RATIO = 16/9
    
    # 默认裁剪帧数
    DEFAULT_CROP_START = 15  # 修改默认开头裁剪帧为15
    DEFAULT_CROP_END = 10
    
    # 支持的图像格式
    IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff']
    VIDEO_EXTENSIONS = [
        '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv',
        '.m4v', '.3gp', '.3g2', '.webm', '.ogv', '.ogg',
        '.mpg', '.mpeg', '.m2v', '.m4p', '.m4b', '.f4v',
        '.f4p', '.f4a', '.f4b', '.vob', '.lrv', '.mxf',
        '.roq', '.nsv', '.amv', '.rm', '.rmvb', '.asf',
        '.ts', '.mts', '.m2ts'
    ]
    
    # UI样式
    PREVIEW_STYLE = """
        QLabel {
            background-color: #181A1F;
            border: 2px dashed #5C6370;
            border-radius: 6px;
            color: #5C6370;
            font-size: 16px;
            font-style: italic;
            padding: 0px;
        }
    """
    
    VIDEO_WIDGET_STYLE = """
        QVideoWidget {
            background-color: #181A1F;
            border: 2px dashed #5C6370;
            border-radius: 6px;
        }
    """
    
    SPLITTER_STYLE = """
        QSplitter::handle {
            background-color: #303238;
            width: 2px;
        }
    """

class PreviewLabel(QLabel):
    """优化的预览标签类，支持图像显示"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()

        self._init_properties()
        self.fill_mode = False  # 是否使用填充模式
    
    def _setup_ui(self):
        """初始化UI设置"""
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMinimumSize(*Constants.MIN_PREVIEW_SIZE)
    

    
    def _init_properties(self):
        """初始化属性"""
        self.original_pixmap = None
        self.aspect_ratio = None

    

    

        
    def resizeEvent(self, event):
        """重写调整大小事件，保持宽高比"""
        super().resizeEvent(event)
        self.update_pixmap()

    def set_pixmap(self, pixmap):
        """设置图像并保持宽高比"""
        self.original_pixmap = pixmap
        if not pixmap.isNull():
            self.aspect_ratio = pixmap.width() / pixmap.height()
        self.update_pixmap()

    def clear(self):
        """清除图像"""
        self.original_pixmap = None
        self.aspect_ratio = None
        super().clear()  # 清除图像和pixmap
    
    def update_pixmap(self):
        """更新显示的图像，保持比例并适合预览区大小"""
        if not (hasattr(self, 'original_pixmap') and self.original_pixmap and not self.original_pixmap.isNull()):
            return
            
        container_size = self._get_container_size()
        scaled_pixmap = self._scale_pixmap_to_fit(self.original_pixmap, container_size)
        super().setPixmap(scaled_pixmap)
    
    def _get_container_size(self) -> tuple:
        """获取容器尺寸"""
        width, height = self.width(), self.height()
        if width <= 0 or height <= 0:
            width, height = 400, 300  # 默认尺寸
        return width, height
    
    def _scale_pixmap_to_fit(self, pixmap: QPixmap, container_size: tuple) -> QPixmap:
        """缩放图像以适应容器，保持宽高比"""
        width, height = container_size

        # 始终保持宽高比，允许有黑边
        return pixmap.scaled(
            width, height,
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )

    def get_actual_image_rect(self):
        """获取图片在控件中的实际显示区域"""
        if not (hasattr(self, 'original_pixmap') and self.original_pixmap and not self.original_pixmap.isNull()):
            # 没有图片时返回整个控件区域
            return QRect(0, 0, self.width(), self.height())

        # 获取控件尺寸
        widget_width = self.width()
        widget_height = self.height()

        # 获取原始图片尺寸
        img_width = self.original_pixmap.width()
        img_height = self.original_pixmap.height()

        # 计算缩放比例（保持宽高比）
        scale_x = widget_width / img_width
        scale_y = widget_height / img_height
        scale = min(scale_x, scale_y)  # 使用较小的缩放比例

        # 计算缩放后的图片尺寸
        scaled_width = int(img_width * scale)
        scaled_height = int(img_height * scale)

        # 计算图片在控件中的位置（居中）
        x = (widget_width - scaled_width) // 2
        y = (widget_height - scaled_height) // 2

        from PyQt6.QtCore import QRect
        return QRect(x, y, scaled_width, scaled_height)



    def mousePressEvent(self, event):
        # 点击功能已禁用
        _ = event  # 忽略未使用的参数
        pass




class VideoExportWorker(QThread):
    """优化的视频导出工作线程"""
    progress_updated = pyqtSignal(int)
    export_finished = pyqtSignal(str, bool, str)

    def __init__(self, video_path: str, output_path: str, cover_image: Optional[QPixmap],
                 fps: float, start_frame: int, end_frame: int, ffmpeg_path: str,
                 export_settings: Optional[Dict[str, Any]] = None, encoder: str = 'libx264'):
        super().__init__()
        self.video_path = video_path
        self.output_path = output_path
        self.cover_image = cover_image
        self.fps = fps
        self.start_frame = start_frame
        self.end_frame = end_frame
        self.ffmpeg_path = ffmpeg_path
        self.encoder = encoder
        print(f"🔧 VideoExportWorker接收到的编码器: {encoder}")

        # 合并导出设置
        self.export_settings = self._merge_export_settings(export_settings)
        print(f"🔧 VideoExportWorker的GPU设置: {self.export_settings.get('use_gpu', False)}")

    def _merge_export_settings(self, user_settings: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """合并用户设置和默认设置"""
        default_settings = {
            'width': 1440, 'height': 2560, 'fps': 30.0,
            'video_codec': 'H.264 (兼容性好)', 'quality_preset': '高质量 (推荐)',
            'use_gpu': True, 'bitrate_mode': 'VBR', 'target_bitrate': 8000,
            'crf_value': 23, 'preset': 'medium', 'audio_codec': 'AAC',
            'audio_bitrate': 128
        }
        if user_settings:
            default_settings.update(user_settings)
        return default_settings





    def _get_audio_encoder(self, output_format: str) -> str:
        """根据输出格式选择合适的音频编码器"""
        audio_encoder_map = {
            'MP4': 'aac',
            'MOV': 'aac',
            'MKV': 'aac',
            'AVI': 'mp3',      # AVI通常使用MP3音频
            'WMV': 'wmav2',    # Windows Media Audio
            'WEBM': 'libvorbis',  # WebM使用Vorbis
            'OGV': 'libvorbis',   # Ogg使用Vorbis
            'FLV': 'aac',         # FLV使用AAC
            '3GP': 'aac',         # 3GP使用AAC
            'M4V': 'aac',         # M4V使用AAC
            'MPEG': 'mp2',        # MPEG使用MP2
            'VOB': 'mp2'          # VOB使用MP2
        }

        return audio_encoder_map.get(output_format, 'aac')

    def _get_encoding_params(self, encoder: str, settings: Dict[str, Any]) -> List[str]:
        """获取编码参数"""
        params = []

        # 获取输出格式
        output_format = settings.get('output_format', 'MP4').upper()
        print(f"编码参数配置 - 格式: {output_format}, 编码器: {encoder}")

        # 根据输出格式选择合适的编码器
        actual_encoder = self._get_compatible_encoder(output_format, encoder)
        print(f"实际使用编码器: {actual_encoder}")

        if actual_encoder == 'libx264':
            # H.264编码参数
            crf = settings.get('crf_value', 23)
            preset = settings.get('preset', 'medium')
            params.extend(['-crf', str(crf), '-preset', preset])

        elif actual_encoder == 'h264_nvenc':
            # NVIDIA硬件编码参数
            bitrate = settings.get('target_bitrate', 8000)
            params.extend(['-b:v', f'{bitrate}k', '-maxrate', f'{bitrate * 2}k'])

        elif actual_encoder == 'h264_qsv':
            # Intel硬件编码参数
            bitrate = settings.get('target_bitrate', 8000)
            params.extend(['-b:v', f'{bitrate}k'])

        elif actual_encoder == 'libxvid':
            # Xvid编码器（用于AVI）
            bitrate = settings.get('target_bitrate', 8000)
            params.extend(['-b:v', f'{bitrate}k'])

        elif actual_encoder == 'libvpx-vp9':
            # VP9编码器（用于WebM）
            crf = settings.get('crf_value', 30)
            params.extend(['-crf', str(crf), '-b:v', '0'])

        # 通用参数
        params.extend(['-pix_fmt', 'yuv420p'])

        # 针对不同格式的特殊处理
        if output_format == 'MOV':
            if actual_encoder == 'h264_nvenc':
                # NVIDIA编码器使用简化参数，避免profile/level冲突
                params.extend(['-movflags', '+faststart'])
            else:
                # 软件编码器可以使用完整参数
                params.extend(['-movflags', '+faststart', '-profile:v', 'high', '-level', '4.0'])
        elif output_format == 'AVI':
            # AVI格式使用较老的兼容性设置
            params.extend(['-vtag', 'XVID'])
        elif output_format == 'MKV':
            # MKV格式支持更多编码选项，使用默认设置
            pass
        elif output_format == 'WEBM':
            # WebM格式特殊处理
            params.extend(['-deadline', 'good', '-cpu-used', '2'])
        elif output_format in ['WMV', 'ASF']:
            # Windows Media格式
            bitrate = settings.get('target_bitrate', 8000)
            params.extend(['-b:v', f'{bitrate}k'])
        elif output_format == 'TS':
            # TS格式特殊处理，使用MPEG-TS容器格式
            params.extend(['-f', 'mpegts'])

        return params

    def _get_video_duration(self, video_path: str) -> float:
        """使用ffprobe获取视频时长（秒）"""
        try:
            ffprobe_path = os.path.join(os.path.dirname(__file__), '..', 'bin', 'ffprobe.exe')
            if not os.path.exists(ffprobe_path):
                ffprobe_path = 'ffprobe'  # 回退到系统PATH

            cmd = [
                ffprobe_path,
                '-v', 'quiet',
                '-show_entries', 'format=duration',
                '-of', 'csv=p=0',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                duration = float(result.stdout.strip())
                print(f"视频时长: {duration:.2f}秒")
                return duration
            else:
                print(f"获取视频时长失败: {result.stderr}")
                return 0.0

        except Exception as e:
            print(f"获取视频时长异常: {e}")
            return 0.0

    def _run_ffmpeg_with_progress(self, cmd: list, start_progress: int, end_progress: int, timeout: int = 180) -> int:
        """运行FFmpeg并实时更新进度"""
        try:
            import re
            import threading

            # 启动FFmpeg进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            # 获取视频总时长
            total_duration = self._get_video_duration(self.video_path) if hasattr(self, 'video_path') else 0

            def read_progress():
                """读取FFmpeg进度输出"""
                while True:
                    line = process.stderr.readline()
                    if not line:
                        break

                    # 解析时间进度 (格式: time=00:01:23.45)
                    time_match = re.search(r'time=(\d+):(\d+):(\d+)\.(\d+)', line)
                    if time_match and total_duration > 0:
                        hours = int(time_match.group(1))
                        minutes = int(time_match.group(2))
                        seconds = int(time_match.group(3))
                        milliseconds = int(time_match.group(4))

                        current_time = hours * 3600 + minutes * 60 + seconds + milliseconds / 100
                        progress_percent = min(current_time / total_duration, 1.0)

                        # 映射到指定的进度范围
                        actual_progress = int(start_progress + progress_percent * (end_progress - start_progress))
                        self.progress_updated.emit(actual_progress)

            # 启动进度读取线程
            progress_thread = threading.Thread(target=read_progress)
            progress_thread.daemon = True
            progress_thread.start()

            # 等待进程完成
            try:
                process.wait(timeout=timeout)
                return process.returncode
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"FFmpeg执行超时 ({timeout}秒)")
                return -1

        except Exception as e:
            print(f"运行FFmpeg失败: {e}")
            return -1

    def run(self):
        """执行视频导出"""
        try:
            # 计算时间参数
            start_time, duration = self._calculate_time_params()

            # 获取目标分辨率
            target_width = self.export_settings['width']
            target_height = self.export_settings['height']

            if self.cover_image:
                self._export_with_cover(target_width, target_height, start_time, duration)
            else:
                self._export_without_cover(start_time, duration)

        except Exception as e:
            self.export_finished.emit(self.output_path, False, str(e))

    def _calculate_time_params(self) -> Tuple[float, float]:
        """计算时间参数"""
        start_time = self.start_frame / self.fps
        duration = (self.end_frame - self.start_frame) / self.fps
        return start_time, duration

    def _export_with_cover(self, target_width: int, target_height: int,
                          start_time: float, duration: float):
        """带封面的导出流程"""
        temp_cover = tempfile.mktemp(suffix='.png')

        try:
            # 调整封面尺寸并保存
            cover_img = self.cover_image.scaled(
                target_width, target_height,
                Qt.AspectRatioMode.IgnoreAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            if not cover_img.save(temp_cover, 'PNG'):
                raise Exception("封面保存失败")

            print(f"目标分辨率: {target_width}x{target_height}")
            print(f"封面保存成功: {os.path.exists(temp_cover)}")

            # 执行带封面的导出
            self.run_with_cover_export(temp_cover, target_width, target_height, start_time, duration)

        finally:
            # 清理临时文件
            if os.path.exists(temp_cover):
                try:
                    os.remove(temp_cover)
                except:
                    pass

    def _export_without_cover(self, start_time: float, duration: float):
        """无封面的导出流程"""
        print("无封面，使用简化导出")
        self.run_without_cover_export(start_time, duration)

    def run_with_cover_export(self, temp_cover: str, frame_width: int, frame_height: int,
                             start_time: float, duration: float):
        """带封面的导出实现"""
        try:
            # 获取导出格式
            output_format = self.export_settings.get('output_format', 'MP4').lower()

            # 分两步：先创建封面视频，再与原视频合并
            temp_cover_video = tempfile.mktemp(suffix=f'.{output_format}')
            temp_main_video = tempfile.mktemp(suffix=f'.{output_format}')

            # 验证输入文件路径
            if not os.path.exists(self.video_path):
                raise FileNotFoundError(f"视频文件不存在: {self.video_path}")
            if not os.path.exists(temp_cover):
                raise FileNotFoundError(f"封面文件不存在: {temp_cover}")

            # 简化处理：暂时跳过音频检测，直接处理
            print(f"开始处理视频: {os.path.basename(self.video_path)}")

            # 步骤1：创建封面视频（简化版本，先确保基本功能工作）
            frame_duration = max(0.1, 1.0 / self.fps)  # 最少0.1秒

            # 获取兼容的编码器
            output_format = self.export_settings.get('output_format', 'MP4').upper()
            compatible_encoder = self._get_compatible_encoder(output_format, self.encoder)

            cmd1 = [
                self.ffmpeg_path, '-y',
                '-loop', '1', '-t', str(frame_duration), '-i', temp_cover,
                '-vf', f'scale={frame_width}:{frame_height}',
                '-c:v', compatible_encoder, '-r', str(self.fps),
                '-an',  # 暂时不添加音频，简化处理
                '-avoid_negative_ts', 'make_zero'
            ]

            # 添加编码器参数
            encoding_params = self._get_encoding_params(compatible_encoder, self.export_settings)
            cmd1.extend(encoding_params)
            cmd1.append(temp_cover_video)

            # 步骤2：处理原视频并缩放到目标分辨率
            cmd2 = [
                self.ffmpeg_path, '-y',
                '-ss', str(start_time), '-t', str(duration), '-i', self.video_path,
                '-vf', f'scale={frame_width}:{frame_height}:force_original_aspect_ratio=increase,crop={frame_width}:{frame_height}',
                '-c:v', compatible_encoder, '-c:a', self._get_audio_encoder(output_format),
                '-r', str(self.fps),
                '-avoid_negative_ts', 'make_zero'
            ]

            # 添加编码器参数
            encoding_params = self._get_encoding_params(compatible_encoder, self.export_settings)
            cmd2.extend(encoding_params)
            cmd2.append(temp_main_video)

            # 步骤3：使用filter_complex合并视频（更稳定的方法）
            cmd3 = [
                self.ffmpeg_path, '-y',
                '-i', temp_cover_video,
                '-i', temp_main_video,
                '-filter_complex', '[0:v][1:v]concat=n=2:v=1[outv]',
                '-map', '[outv]', '-map', '1:a?',  # 音频轨道可选
                '-c:v', compatible_encoder, '-c:a', self._get_audio_encoder(output_format),
                '-avoid_negative_ts', 'make_zero'
            ]

            # 添加编码器参数
            encoding_params = self._get_encoding_params(compatible_encoder, self.export_settings)
            cmd3.extend(encoding_params)
            cmd3.append(self.output_path)

            print("=" * 60)
            print("🎬 开始视频导出处理")
            print(f"📁 封面文件: {temp_cover}")
            print(f"📁 输出文件: {self.output_path}")
            print(f"📺 输出格式: {self.export_settings.get('output_format', 'MP4')}")
            print(f"🔧 兼容编码器: {compatible_encoder}")
            print(f"📐 目标分辨率: {frame_width}x{frame_height}")
            print("=" * 60)

            # 分步执行ffmpeg命令
            print("开始分步执行ffmpeg...")
            self.progress_updated.emit(5)  # 开始进度

            try:
                # 步骤1：创建封面视频
                print("步骤1：创建封面视频")
                print(f"步骤1命令: {' '.join(cmd1)}")
                result1 = self._run_ffmpeg_with_progress(cmd1, 0, 20, timeout=60)  # 0-20%
                if result1 != 0:
                    print(f"步骤1失败，返回码: {result1}")
                    print(f"输出格式: {self.export_settings.get('output_format', 'MP4')}")
                    raise Exception(f"创建封面视频失败，返回码: {result1}")

                # 步骤2：处理原视频
                print("步骤2：处理原视频")
                print(f"步骤2命令: {' '.join(cmd2)}")
                result2 = self._run_ffmpeg_with_progress(cmd2, 20, 70, timeout=300)  # 20-70%
                if result2 != 0:
                    print(f"步骤2失败，返回码: {result2}")
                    raise Exception(f"处理原视频失败，返回码: {result2}")
                else:
                    print("步骤2成功")

                # 步骤3：合并视频（使用更短的超时时间）
                print("步骤3：合并视频")
                print(f"合并命令: {' '.join(cmd3)}")

                # 修改超时时间为180秒，支持更长的合成时间
                return_code = self._run_ffmpeg_with_progress(cmd3, 70, 100, timeout=180)  # 70-100%

                if return_code != 0:
                    print(f"步骤3失败，返回码: {return_code}")
                else:
                    print("步骤3成功")

            except subprocess.TimeoutExpired:
                print("ffmpeg执行超时，尝试备用方案")
                return_code = self._try_fallback_methods(temp_cover, start_time, duration, frame_width, frame_height)
            except Exception as e:
                print(f"分步执行失败: {e}")
                # 尝试多种回退方案
                return_code = self._try_fallback_methods(temp_cover, start_time, duration, frame_width, frame_height)

            # 清理临时文件
            try:
                if os.path.exists(temp_cover_video):
                    os.remove(temp_cover_video)
                if os.path.exists(temp_main_video):
                    os.remove(temp_main_video)
                # concat_file 已不再使用，移除相关清理代码
            except Exception as e:
                print(f"清理临时文件失败: {e}")

            if return_code == 0:
                self.export_finished.emit(self.output_path, True, "")
            else:
                # 提供更详细的错误信息
                error_msg = f"ffmpeg进程返回错误代码: {return_code}"
                if return_code == -22 or return_code == 4294967274:
                    error_msg += " (参数错误，可能是文件名包含特殊字符)"
                elif return_code == -2:
                    error_msg += " (文件未找到)"
                elif return_code == -13:
                    error_msg += " (权限被拒绝)"
                self.export_finished.emit(self.output_path, False, error_msg)

        except Exception as e:
            self.export_finished.emit(self.output_path, False, str(e))

    def run_without_cover_export(self, start_time: float, duration: float):
        """无封面的导出实现"""
        try:
            print("开始简化导出（无封面）...")

            # 获取目标分辨率
            target_width = self.export_settings.get('width', 1440)
            target_height = self.export_settings.get('height', 2560)

            # 简化命令：直接处理视频
            cmd = [
                self.ffmpeg_path, '-y',
                '-ss', str(start_time), '-t', str(duration), '-i', self.video_path,
                '-vf', f'scale={target_width}:{target_height}',
                '-c:v', self.encoder, '-c:a', 'aac',
                '-r', str(self.fps)
            ]

            # 添加编码器参数
            encoding_params = self._get_encoding_params(self.encoder, self.export_settings)
            cmd.extend(encoding_params)
            cmd.append(self.output_path)

            print(f"简化导出命令: {' '.join(cmd)}")

            # 执行命令
            self.progress_updated.emit(20)
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=300)
            return_code = result.returncode

            self.progress_updated.emit(100)

            if return_code == 0:
                self.export_finished.emit(self.output_path, True, "")
            else:
                self.export_finished.emit(self.output_path, False, f"ffmpeg进程返回错误代码: {return_code}")

        except Exception as e:
            print(f"简化导出失败: {e}")
            self.export_finished.emit(self.output_path, False, str(e))

    def _try_fallback_methods(self, temp_cover: str, start_time: float, duration: float, frame_width: int, frame_height: int):
        """尝试多种回退方案"""
        print("开始尝试回退方案...")

        # 方案1：使用更简单的filter_complex
        try:
            print("尝试方案1：简化的filter_complex")
            return_code = self._fallback_simple_concat(temp_cover, start_time, duration, frame_width, frame_height)
            if return_code == 0:
                print("方案1成功")
                return return_code
        except Exception as e:
            print(f"方案1失败: {e}")

        # 方案2：无封面导出
        try:
            print("尝试方案2：无封面导出")
            return_code = self._fallback_no_cover_export(start_time, duration, frame_width, frame_height)
            if return_code == 0:
                print("方案2成功（无封面）")
                return return_code
        except Exception as e:
            print(f"方案2失败: {e}")

        print("所有回退方案都失败")
        return -1

    def _fallback_simple_concat(self, temp_cover: str, start_time: float, duration: float, frame_width: int, frame_height: int):
        """简化的concat方案"""
        try:
            cmd = [
                self.ffmpeg_path, '-y',
                '-loop', '1', '-t', '0.1', '-i', temp_cover,
                '-ss', str(start_time), '-t', str(duration), '-i', self.video_path,
                '-filter_complex',
                f'[0:v]scale={frame_width}:{frame_height}:force_original_aspect_ratio=increase,crop={frame_width}:{frame_height}[cover];'
                f'[1:v]scale={frame_width}:{frame_height}:force_original_aspect_ratio=increase,crop={frame_width}:{frame_height}[main];'
                f'[cover][main]concat=n=2:v=1[outv]',
                '-map', '[outv]', '-map', '1:a?',  # 使用?表示音频轨道可选
                '-c:v', 'libx264', '-preset', 'ultrafast',
                '-c:a', 'aac',  # 重新编码音频确保兼容性
                '-avoid_negative_ts', 'make_zero',
                self.output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=120)
            return result.returncode

        except Exception as e:
            print(f"简化concat失败: {e}")
            return -1

    def _fallback_no_cover_export(self, start_time: float, duration: float, frame_width: int, frame_height: int):
        """无封面的回退导出"""
        try:
            cmd = [
                self.ffmpeg_path, '-y',
                '-ss', str(start_time), '-t', str(duration), '-i', self.video_path,
                '-vf', f'scale={frame_width}:{frame_height}:force_original_aspect_ratio=increase,crop={frame_width}:{frame_height}',
                '-c:v', 'libx264', '-preset', 'fast',
                '-c:a', 'aac',
                '-avoid_negative_ts', 'make_zero',
                self.output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=120)
            return result.returncode

        except Exception as e:
            print(f"无封面导出失败: {e}")
            return -1

    def _get_encoding_params(self, encoder: str, settings: Dict[str, Any]) -> List[str]:
        """获取编码参数"""
        params = []

        # 获取输出格式
        output_format = settings.get('output_format', 'MP4').upper()
        print(f"编码参数配置 - 格式: {output_format}, 编码器: {encoder}")

        # 根据输出格式选择合适的编码器
        actual_encoder = self._get_compatible_encoder(output_format, encoder)
        print(f"实际使用编码器: {actual_encoder}")

        if actual_encoder == 'libx264':
            # H.264编码参数
            crf = settings.get('crf_value', 23)
            preset = settings.get('preset', 'medium')
            params.extend(['-crf', str(crf), '-preset', preset])

        elif actual_encoder == 'h264_nvenc':
            # NVIDIA硬件编码参数
            bitrate = settings.get('target_bitrate', 8000)
            params.extend(['-b:v', f'{bitrate}k', '-maxrate', f'{bitrate * 2}k'])

        elif actual_encoder == 'h264_qsv':
            # Intel硬件编码参数
            bitrate = settings.get('target_bitrate', 8000)
            params.extend(['-b:v', f'{bitrate}k'])

        elif actual_encoder == 'libxvid':
            # Xvid编码器（用于AVI）
            bitrate = settings.get('target_bitrate', 8000)
            params.extend(['-b:v', f'{bitrate}k'])

        elif actual_encoder == 'libvpx-vp9':
            # VP9编码器（用于WebM）
            crf = settings.get('crf_value', 30)
            params.extend(['-crf', str(crf), '-b:v', '0'])

        # 通用参数
        params.extend(['-pix_fmt', 'yuv420p'])

        # 针对不同格式的特殊处理
        if output_format == 'MOV':
            if actual_encoder == 'h264_nvenc':
                # NVIDIA编码器使用简化参数，避免profile/level冲突
                params.extend(['-movflags', '+faststart'])
            else:
                # 软件编码器可以使用完整参数
                params.extend(['-movflags', '+faststart', '-profile:v', 'high', '-level', '4.0'])
        elif output_format == 'AVI':
            # AVI格式使用较老的兼容性设置
            params.extend(['-vtag', 'XVID'])
        elif output_format == 'MKV':
            # MKV格式支持更多编码选项，使用默认设置
            pass
        elif output_format == 'WEBM':
            # WebM格式特殊处理
            params.extend(['-deadline', 'good', '-cpu-used', '2'])
        elif output_format in ['WMV', 'ASF']:
            # Windows Media格式
            bitrate = settings.get('target_bitrate', 8000)
            params.extend(['-b:v', f'{bitrate}k'])
        elif output_format == 'TS':
            # TS格式特殊处理，使用MPEG-TS容器格式
            params.extend(['-f', 'mpegts'])

        return params

    def _get_compatible_encoder(self, output_format: str, preferred_encoder: str) -> str:
        """根据输出格式选择兼容的编码器"""
        format_encoder_map = {
            'MP4': 'libx264',
            'MOV': 'libx264',
            'MKV': 'libx264',
            'AVI': 'libxvid',  # AVI格式使用Xvid编码器
            'WMV': 'wmv2',     # Windows Media Video
            'WEBM': 'libvpx-vp9',  # WebM使用VP9
            'OGV': 'libtheora',    # Ogg使用Theora
            'FLV': 'libx264',      # FLV使用H.264
            '3GP': 'libx264',      # 3GP使用H.264
            'M4V': 'libx264',      # M4V使用H.264
            'MPEG': 'mpeg2video',  # MPEG使用MPEG-2
            'VOB': 'mpeg2video',   # VOB使用MPEG-2
            'TS': 'libx264'        # TS格式使用H.264
        }

        # 获取推荐的编码器
        recommended_encoder = format_encoder_map.get(output_format, 'libx264')

        # 扩展GPU硬件编码器支持的格式
        gpu_supported_formats = ['MP4', 'MOV', 'MKV', 'FLV', '3GP', 'M4V', 'TS']

        # 智能编码器选择：根据格式自动选择最佳编码器
        if output_format in gpu_supported_formats:
            # 对于支持GPU的格式，优先检查GPU编码器可用性
            if preferred_encoder in ['h264_nvenc', 'h264_qsv']:
                print(f"✅ 格式 {output_format} 使用用户指定的GPU编码器: {preferred_encoder}")
                return preferred_encoder
            else:
                # 用户没有指定GPU编码器，但格式支持，自动尝试GPU加速
                print(f"🚀 格式 {output_format} 支持GPU加速，自动尝试使用GPU编码器")
                # 检查GPU编码器是否可用
                if self._is_gpu_encoder_available():
                    gpu_encoder = self._get_available_gpu_encoder()
                    if gpu_encoder:
                        print(f"✅ 自动选择GPU编码器: {gpu_encoder}")
                        return gpu_encoder

                print(f"⚠️ GPU编码器不可用，使用软件编码器: {recommended_encoder}")
                return recommended_encoder
        else:
            # 对于不支持GPU的格式，使用专用编码器
            if preferred_encoder in ['h264_nvenc', 'h264_qsv']:
                print(f"❌ 格式 {output_format} 不支持GPU硬件编码器，使用推荐编码器: {recommended_encoder}")
            else:
                print(f"📝 格式 {output_format} 使用专用编码器: {recommended_encoder}")
            return recommended_encoder

    def _is_gpu_encoder_available(self) -> bool:
        """检查GPU编码器是否可用"""
        try:
            ffmpeg_path = self._get_ffmpeg_path()
            result = subprocess.run([ffmpeg_path, '-encoders'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                encoders = result.stdout
                return 'h264_nvenc' in encoders or 'h264_qsv' in encoders
            return False
        except Exception:
            return False

    def _get_available_gpu_encoder(self) -> str:
        """获取可用的GPU编码器"""
        try:
            ffmpeg_path = self._get_ffmpeg_path()
            result = subprocess.run([ffmpeg_path, '-encoders'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                encoders = result.stdout
                # 优先级：NVIDIA > Intel
                if 'h264_nvenc' in encoders:
                    return 'h264_nvenc'
                elif 'h264_qsv' in encoders:
                    return 'h264_qsv'
            return ''
        except Exception:
            return ''

    def _get_audio_encoder(self, output_format: str) -> str:
        """根据输出格式选择合适的音频编码器"""
        audio_encoder_map = {
            'MP4': 'aac',
            'MOV': 'aac',
            'MKV': 'aac',
            'AVI': 'mp3',      # AVI通常使用MP3音频
            'WMV': 'wmav2',    # Windows Media Audio
            'WEBM': 'libvorbis',  # WebM使用Vorbis
            'OGV': 'libvorbis',   # Ogg使用Vorbis
            'FLV': 'aac',         # FLV使用AAC
            '3GP': 'aac',         # 3GP使用AAC
            'M4V': 'aac',         # M4V使用AAC
            'MPEG': 'mp2',        # MPEG使用MP2
            'VOB': 'mp2',         # VOB使用MP2
            'TS': 'aac'           # TS格式使用AAC音频
        }

        return audio_encoder_map.get(output_format, 'aac')

    def _fallback_simple_export(self, start_time: float, duration: float,
                               frame_width: int, frame_height: int) -> int:
        """回退到简单导出模式"""
        try:
            print("回退到简单模式（带分辨率缩放，无封面）")

            # 获取导出设置
            output_format = self.export_settings.get('output_format', 'MP4').upper()

            simple_cmd = [
                self.ffmpeg_path, '-y',
                '-ss', str(start_time), '-t', str(duration), '-i', self.video_path,
                '-vf', f'scale={frame_width}:{frame_height}:force_original_aspect_ratio=increase,crop={frame_width}:{frame_height}',
                '-c:v', 'libx264', '-c:a', 'aac',
                '-r', str(self.fps)
            ]

            # 添加格式特定参数
            if output_format == 'MOV':
                if self.encoder == 'h264_nvenc':
                    # NVIDIA编码器使用简化参数，避免profile/level冲突
                    simple_cmd.extend(['-movflags', '+faststart'])
                else:
                    # 软件编码器可以使用完整参数
                    simple_cmd.extend(['-movflags', '+faststart', '-profile:v', 'high', '-level', '4.0'])

            simple_cmd.extend(['-pix_fmt', 'yuv420p', self.output_path])

            print(f"简化导出命令: {' '.join(simple_cmd)}")

            result = subprocess.run(simple_cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', timeout=300)

            if result.returncode != 0:
                print(f"简化导出失败，错误信息: {result.stderr}")

            return result.returncode

        except Exception as e:
            print(f"简单模式也失败: {e}")
            return -1


class CoverEditOptimizedModule(BaseModule):
    """优化的封面编辑模块"""

    def __init__(self, parent=None):
        # 先初始化属性（在父类初始化之前）
        self._init_properties()

        # 调用父类初始化（会自动调用init_ui）
        super().__init__(parent)

        # 设置快捷键（需要在父类初始化后）
        self._setup_shortcuts()



    def _init_properties(self):
        """初始化模块属性"""
        # 图像相关
        self.original_image = None
        self.base_image = None
        self.current_image = None

        # 视频相关
        self.video_path = ""
        self.video_cap = None
        self.total_frames = 0
        self.fps = 0
        self.duration = 0
        self.current_frame = 0

        # 裁剪设置
        self.cropped_start = 0.0
        self.cropped_end = 0.0
        self.cropped_start_frames = Constants.DEFAULT_CROP_START
        self.cropped_end_frames = Constants.DEFAULT_CROP_END

        # 媒体播放器
        self.media_player = None
        self.export_thread = None

        # 图层系统
        self.cover_layers = []
        self.layer_counter = 0
        self.current_layer_index = -1
        self.original_image = None

        # 视频分辨率信息
        self.video_width, self.video_height = Constants.DEFAULT_VIDEO_SIZE
        self.video_aspect_ratio = Constants.DEFAULT_ASPECT_RATIO

        # 路径记忆（使用全局路径管理器）
        self.last_video_path = get_path('cover_edit_video')
        self.last_image_path = get_path('cover_edit_image')
        self.last_export_path = get_path('cover_edit_export')

        # 批量处理日志显示状态
        self.batch_log_visible = False

        # 编辑器模块
        self.export_settings = None



        # 批量处理相关 - 优化版本
        self.batch_mode_enabled = False
        self.batch_settings_shown = False  # 记录设置是否已显示
        self.file_watcher = None
        self.watch_folder = ""
        self.output_folder = ""
        self.processing_queue = []
        self.current_processing = False
        self.processed_files = set()
        self.failed_files = set()
        self.retry_count = {}
        self.temp_converted_files = []
        self.scan_in_progress = False

        # 新增优化相关属性
        self._processing_lock = False  # 处理状态锁
        self._file_check_timers = {}  # 文件检查定时器
        self._max_retries = 3  # 最大重试次数
        self._retry_delays = [2000, 5000, 10000]  # 重试延迟（毫秒）
        self._memory_check_timer = None  # 内存检查定时器
        self._last_memory_check = 0  # 上次内存检查时间

    def _setup_shortcuts(self):
        """设置快捷键"""
        try:
            from PyQt6.QtGui import QShortcut, QKeySequence
            from PyQt6.QtCore import Qt

            # 设置焦点策略，使窗口能接收键盘事件
            self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

            # Ctrl+1: 切换批量处理日志显示
            self.log_toggle_shortcut = QShortcut(QKeySequence("Ctrl+1"), self)
            self.log_toggle_shortcut.activated.connect(self.toggle_batch_log_display)

            print("📋 快捷键设置完成: Ctrl+1 切换批量处理日志")

        except Exception as e:
            print(f"设置快捷键失败: {e}")

    def init_ui(self):
        """初始化用户界面"""
        try:
            self._init_editors()
            self._setup_ui()
            self._setup_media_player()
            self._setup_shortcuts()
            # self._load_default_cover()  # 禁用自动加载默认封面

        except Exception as e:
            print(f"初始化UI失败: {e}")
            QMessageBox.critical(self, Constants.MSG_ERROR, f"初始化UI失败: {str(e)}")

    def _init_editors(self):
        """初始化编辑器模块"""
        try:

            if not hasattr(self, 'export_settings') or self.export_settings is None:
                # 创建独立的导出设置窗口
                self.export_settings = VideoExportSettings()
        except Exception as e:
            print(f"初始化编辑器模块失败: {e}")
            QMessageBox.critical(self, Constants.MSG_ERROR, f"初始化编辑器模块失败: {str(e)}")

    def _setup_ui(self):
        """设置用户界面"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 创建分割器
        self.splitter = self._create_splitter()

        # 创建左右面板
        left_panel = self._create_left_panel()
        right_panel = self._create_right_panel()

        # 添加面板到分割器
        self.splitter.addWidget(left_panel)
        self.splitter.addWidget(right_panel)

        # 设置1:1分配 - 使用具体像素值
        self.splitter.setSizes([500, 500])  # 左右栏1:1分配

        main_layout.addWidget(self.splitter)

        # 应用基础样式
        self.setStyleSheet(self._get_base_style())

    def _create_splitter(self) -> QSplitter:
        """创建分割器"""
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setStyleSheet(Constants.SPLITTER_STYLE)
        # 设置拉伸因子为相等
        splitter.setStretchFactor(0, 1)  # 左侧面板
        splitter.setStretchFactor(1, 1)  # 右侧面板
        return splitter

    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(10)

        # 创建预览区域
        self._create_preview_area(left_layout)

        # 创建控制按钮
        self._create_control_buttons(left_layout)

        # 创建控制面板
        self._create_control_panels(left_layout)

        # 导出进度条（放在左侧栏最下方，默认隐藏）
        self.export_progress = QProgressBar()
        self.export_progress.setVisible(False)  # 默认隐藏
        self.export_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 4px;
                text-align: center;
                background-color: #2b2b2b;
                color: white;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        left_layout.addWidget(self.export_progress)

        return left_panel

    def _create_preview_area(self, layout: QVBoxLayout):
        """创建预览区域"""
        # 视频预览区域 - 使用堆叠窗口
        self.preview_stack = QStackedWidget()
        self.preview_stack.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # 截图模式预览
        self.snapshot_preview = PreviewLabel()
        self.snapshot_preview.setMinimumSize(500, 300)
        self.snapshot_preview.setStyleSheet(Constants.PREVIEW_STYLE)
        self.snapshot_preview.setText("截图模式预览")

        # 播放模式预览
        self.playback_widget = self._create_playback_widget()

        # 添加到堆叠窗口
        self.preview_stack.addWidget(self.snapshot_preview)
        self.preview_stack.addWidget(self.playback_widget)

        layout.addWidget(self.preview_stack)

    def _create_playback_widget(self) -> QWidget:
        """创建播放模式组件"""
        playback_widget = QWidget()
        playback_layout = QVBoxLayout(playback_widget)
        playback_layout.setContentsMargins(0, 0, 0, 0)
        playback_layout.setSpacing(0)

        # 视频播放器容器
        self.video_container = QWidget()
        video_container_layout = QVBoxLayout(self.video_container)
        video_container_layout.setContentsMargins(0, 0, 0, 0)
        video_container_layout.setSpacing(0)

        # 视频播放器
        self.video_widget = QVideoWidget()
        self.video_widget.setAspectRatioMode(Qt.AspectRatioMode.KeepAspectRatio)
        self.video_widget.setMinimumSize(500, 300)
        self.video_widget.setStyleSheet(Constants.VIDEO_WIDGET_STYLE)

        video_container_layout.addWidget(self.video_widget)
        playback_layout.addWidget(self.video_container)

        return playback_widget

    def _create_control_buttons(self, layout: QVBoxLayout):
        """创建控制按钮"""
        # 模式按钮行
        mode_button_layout = QHBoxLayout()
        mode_button_layout.setContentsMargins(0, 5, 0, 5)
        mode_button_layout.setSpacing(10)

        # 创建按钮组确保单选行为
        self.mode_button_group = QButtonGroup(self)

        # 创建各种按钮
        self._create_mode_buttons(mode_button_layout)
        self._create_action_buttons(mode_button_layout)

        layout.addLayout(mode_button_layout)

    def _create_mode_buttons(self, layout: QHBoxLayout):
        """创建模式切换按钮"""
        button_style = self._get_button_style()

        # 播放模式按钮
        self.play_mode_btn = QPushButton("播放模式")
        self.play_mode_btn.setCheckable(True)
        self.play_mode_btn.setFixedWidth(120)
        self.play_mode_btn.setStyleSheet(button_style['mode'])
        self.play_mode_btn.clicked.connect(self.switch_to_play_mode)

        # 截图模式按钮
        self.snapshot_mode_btn = QPushButton("截图模式")
        self.snapshot_mode_btn.setCheckable(True)
        self.snapshot_mode_btn.setChecked(True)  # 默认选中
        self.snapshot_mode_btn.setFixedWidth(120)
        self.snapshot_mode_btn.setStyleSheet(button_style['mode'])
        self.snapshot_mode_btn.clicked.connect(self.switch_to_snapshot_mode)

        # 添加到按钮组
        self.mode_button_group.addButton(self.play_mode_btn, 0)
        self.mode_button_group.addButton(self.snapshot_mode_btn, 1)

        layout.addWidget(self.play_mode_btn)
        layout.addWidget(self.snapshot_mode_btn)

    def _create_action_buttons(self, layout: QHBoxLayout):
        """创建操作按钮"""
        button_style = self._get_button_style()

        # 截取帧按钮
        self.capture_frame_btn = QPushButton("截取当前帧")
        self.capture_frame_btn.setFixedWidth(120)
        self.capture_frame_btn.setStyleSheet(button_style['action'])
        self.capture_frame_btn.clicked.connect(self.capture_current_frame)

        # 智能截取按钮
        self.smart_capture_btn = QPushButton("智能截取")
        self.smart_capture_btn.setFixedWidth(120)
        self.smart_capture_btn.setStyleSheet(button_style['smart'])
        self.smart_capture_btn.clicked.connect(self.smart_capture_frame)

        # 导入封面按钮
        self.import_cover_btn = QPushButton("导入封面")
        self.import_cover_btn.setFixedWidth(120)
        self.import_cover_btn.setStyleSheet(button_style['import'])
        self.import_cover_btn.clicked.connect(self.import_cover_image)

        layout.addWidget(self.capture_frame_btn)
        layout.addWidget(self.smart_capture_btn)
        layout.addWidget(self.import_cover_btn)

    def _get_button_style(self) -> Dict[str, str]:
        """获取按钮样式"""
        return {
            'mode': """
                QPushButton {
                    background-color: #3A3F4B;
                    color: #ABB2BF;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:checked {
                    background-color: #61AFEF;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #4A4F5B;
                }
            """,
            'action': """
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #F57C00;
                }
            """,
            'smart': """
                QPushButton {
                    background-color: #9C27B0;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #7B1FA2;
                }
            """,
            'import': """
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #43A047;
                }
            """
        }

    def _create_control_panels(self, layout: QVBoxLayout):
        """创建控制面板"""
        # 控制面板堆叠区域
        self.control_stack = QStackedWidget()
        self.control_stack.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.control_stack.setMaximumHeight(60)

        # 创建播放和截图控制面板
        play_control_panel = self._create_play_control_panel()
        snapshot_control_panel = self._create_snapshot_control_panel()

        self.control_stack.addWidget(play_control_panel)
        self.control_stack.addWidget(snapshot_control_panel)
        self.control_stack.setCurrentIndex(1)  # 默认显示截图控制面板

        layout.addWidget(self.control_stack)

        # 添加视频信息组
        info_group = self._create_video_info_group()
        layout.addWidget(info_group)

    def _create_play_control_panel(self) -> QWidget:
        """创建播放控制面板"""
        panel = QWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 播放/暂停按钮
        self.play_pause_btn = QPushButton("播放")
        self.play_pause_btn.setFixedSize(80, 40)
        self.play_pause_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45A049;
            }
        """)
        self.play_pause_btn.clicked.connect(self.toggle_playback)

        # 进度条
        self.playback_slider = QSlider(Qt.Orientation.Horizontal)
        self.playback_slider.setMinimum(0)
        self.playback_slider.setMaximum(100)
        self.playback_slider.setValue(0)
        self.playback_slider.setStyleSheet(self._get_slider_style("#4CAF50"))

        # 时间标签
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setMinimumWidth(100)
        self.time_label.setStyleSheet("color: #ABB2BF; font-size: 12px;")

        layout.addWidget(self.play_pause_btn)
        layout.addWidget(self.playback_slider, 1)
        layout.addWidget(self.time_label)

        return panel

    def _create_snapshot_control_panel(self) -> QWidget:
        """创建截图控制面板"""
        panel = QWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 帧滑块
        self.frame_slider = QSlider(Qt.Orientation.Horizontal)
        self.frame_slider.setMinimum(0)
        self.frame_slider.setMaximum(100)
        self.frame_slider.setValue(0)
        self.frame_slider.setStyleSheet(self._get_slider_style("#FF9800"))
        self.frame_slider.valueChanged.connect(self.on_frame_slider_changed)

        # 帧数显示
        self.frame_label = QLabel("帧: 0/0")
        self.frame_label.setMinimumWidth(100)
        self.frame_label.setStyleSheet("color: #ABB2BF; font-size: 12px;")

        layout.addWidget(self.frame_slider, 1)
        layout.addWidget(self.frame_label)

        return panel

    def _get_slider_style(self, color: str) -> str:
        """获取滑块样式"""
        return f"""
            QSlider::groove:horizontal {{
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {color};
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }}
            QSlider::handle:horizontal:hover {{
                background: {color};
                opacity: 0.8;
            }}
        """

    def _create_video_info_group(self) -> QGroupBox:
        """创建视频信息组"""
        info_group = QGroupBox("视频信息与裁剪设置")
        info_layout = QGridLayout(info_group)
        info_layout.setContentsMargins(10, 15, 10, 10)
        info_layout.setSpacing(8)

        # 视频路径
        info_layout.addWidget(QLabel("视频文件:"), 0, 0)
        self.video_path_edit = QLineEdit()
        self.video_path_edit.setReadOnly(True)
        self.video_path_edit.setPlaceholderText("请选择视频文件...")
        info_layout.addWidget(self.video_path_edit, 0, 1, 1, 2)

        self.browse_video_btn = QPushButton("浏览")
        self.browse_video_btn.setFixedWidth(80)
        self.browse_video_btn.clicked.connect(self.browse_video_file)
        info_layout.addWidget(self.browse_video_btn, 0, 3)

        # 视频信息显示
        info_layout.addWidget(QLabel("分辨率:"), 1, 0)
        self.resolution_label = QLabel("未知")
        self.resolution_label.setStyleSheet("color: #98C379;")
        info_layout.addWidget(self.resolution_label, 1, 1)

        info_layout.addWidget(QLabel("时长:"), 1, 2)
        self.duration_label = QLabel("未知")
        self.duration_label.setStyleSheet("color: #98C379;")
        info_layout.addWidget(self.duration_label, 1, 3)

        # 裁剪设置
        info_layout.addWidget(QLabel("开头裁剪(帧):"), 2, 0)
        self.crop_start_input = QLineEdit(str(Constants.DEFAULT_CROP_START))
        self.crop_start_input.setFixedWidth(80)
        self.crop_start_input.setPlaceholderText("0=不裁剪")  # 添加占位符提示
        self.crop_start_input.textChanged.connect(self.on_crop_settings_changed)
        info_layout.addWidget(self.crop_start_input, 2, 1)

        info_layout.addWidget(QLabel("结尾裁剪(帧):"), 2, 2)
        self.crop_end_input = QLineEdit(str(Constants.DEFAULT_CROP_END))
        self.crop_end_input.setFixedWidth(80)
        self.crop_end_input.setPlaceholderText("0=不裁剪")  # 添加占位符提示
        self.crop_end_input.textChanged.connect(self.on_crop_settings_changed)
        info_layout.addWidget(self.crop_end_input, 2, 3)

        return info_group

    def _create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(10)

        # 封面预览
        self._create_cover_preview(right_layout)



        # 导出按钮（包含功能按钮）
        self._create_export_button(right_layout)

        # 批量处理组
        batch_group = self._create_batch_group()
        right_layout.addWidget(batch_group)

        return right_panel

    def _create_cover_preview(self, layout: QVBoxLayout):
        """创建封面预览"""
        # 创建封面预览容器
        cover_container = QWidget()
        cover_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        cover_layout = QVBoxLayout(cover_container)
        cover_layout.setContentsMargins(0, 0, 0, 0)
        cover_layout.setSpacing(0)

        # 创建封面预览标签
        self.cover_preview = PreviewLabel()
        self.cover_preview.setStyleSheet(Constants.PREVIEW_STYLE)
        self.cover_preview.setText("请先导入视频\n系统将自动匹配合适的封面")



        # 初始化尺寸记录
        self._last_preview_size = None

        # 保存原始的 resizeEvent 方法
        self.cover_preview._original_resizeEvent = self.cover_preview.resizeEvent

        # 创建新的 resizeEvent 方法，调用原始方法
        def combined_resizeEvent(event):
            self.cover_preview._original_resizeEvent(event)  # 调用原始缩放逻辑

        self.cover_preview.resizeEvent = combined_resizeEvent

        # 添加预览到容器
        cover_layout.addWidget(self.cover_preview)

        # 添加容器到主布局，使用拉伸因子使预览区占据更多空间
        layout.addWidget(cover_container, 1)













    def _create_composite_cover(self):
        """创建包含所有图层的合成封面图像，用于导出"""
        # 直接返回基础封面
        return self.base_image if hasattr(self, 'base_image') and self.base_image else None





    def qpixmap_to_pil(self, qpixmap):
        """将QPixmap转换为PIL Image"""
        try:
            # 转换为QImage
            qimage = qpixmap.toImage()

            # 转换为RGBA格式
            qimage = qimage.convertToFormat(QImage.Format.Format_RGBA8888)

            # 获取图像数据
            width = qimage.width()
            height = qimage.height()
            ptr = qimage.constBits()
            arr = np.array(ptr).reshape(height, width, 4)

            # 创建PIL Image
            return Image.fromarray(arr, 'RGBA')

        except Exception as e:
            print(f"QPixmap转PIL失败: {e}")
            return None

    def pil_to_qpixmap(self, pil_image):
        """将PIL Image转换为QPixmap"""
        try:
            # 转换为numpy数组
            if pil_image.mode != 'RGBA':
                pil_image = pil_image.convert('RGBA')

            arr = np.array(pil_image)
            height, width, channel = arr.shape
            bytes_per_line = 4 * width

            # 创建QImage
            qimage = QImage(arr.data, width, height, bytes_per_line, QImage.Format.Format_RGBA8888)

            # 转换为QPixmap
            return QPixmap.fromImage(qimage)

        except Exception as e:
            print(f"PIL转QPixmap失败: {e}")
            return None



    def _get_actual_image_rect(self):
        """获取封面图片在预览控件中的实际显示区域"""
        try:
            if not (hasattr(self.cover_preview, 'original_pixmap') and
                   self.cover_preview.original_pixmap and
                   not self.cover_preview.original_pixmap.isNull()):
                # 没有图片时返回整个预览区域
                from PyQt6.QtCore import QRect
                return QRect(0, 0, self.cover_preview.width(), self.cover_preview.height())

            # 获取预览控件尺寸
            widget_width = self.cover_preview.width()
            widget_height = self.cover_preview.height()

            # 获取原始图片尺寸
            img_width = self.cover_preview.original_pixmap.width()
            img_height = self.cover_preview.original_pixmap.height()

            # 计算保持宽高比的缩放
            scale_x = widget_width / img_width
            scale_y = widget_height / img_height
            scale = min(scale_x, scale_y)  # 使用较小的缩放比例

            # 计算缩放后的图片尺寸
            scaled_width = int(img_width * scale)
            scaled_height = int(img_height * scale)

            # 计算图片在控件中的居中位置
            x = (widget_width - scaled_width) // 2
            y = (widget_height - scaled_height) // 2

            from PyQt6.QtCore import QRect
            return QRect(x, y, scaled_width, scaled_height)

        except Exception as e:
            print(f"计算图片显示区域失败: {e}")
            from PyQt6.QtCore import QRect
            return QRect(0, 0, self.cover_preview.width(), self.cover_preview.height())

    def _create_export_button(self, layout: QVBoxLayout):
        """创建导出按钮区域"""
        # 创建导出区域的水平布局
        export_layout = QHBoxLayout()
        export_layout.setSpacing(10)

        # 统一的按钮样式
        button_style = """
            QPushButton {
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:disabled {
                background-color: #5C6370;
                color: #ABB2BF;
            }
        """



        # 文字编辑按钮
        self.text_edit_btn_export = QPushButton("文字编辑")
        self.text_edit_btn_export.setFixedHeight(50)
        self.text_edit_btn_export.setStyleSheet(button_style + """
            QPushButton {
                background-color: #61AFEF;
            }
            QPushButton:hover {
                background-color: #528BFF;
            }
        """)
        self.text_edit_btn_export.clicked.connect(self.open_text_properties)
        # 初始状态禁用，直到有封面图片
        self.text_edit_btn_export.setEnabled(False)

        # 导出设置按钮
        self.export_settings_btn_export = QPushButton("导出设置")
        self.export_settings_btn_export.setFixedHeight(50)
        self.export_settings_btn_export.setStyleSheet(button_style + """
            QPushButton {
                background-color: #98C379;
            }
            QPushButton:hover {
                background-color: #7FB069;
            }
        """)
        self.export_settings_btn_export.clicked.connect(self.open_export_settings)

        # 导出视频按钮
        self.export_btn = QPushButton("导出视频")
        self.export_btn.setFixedHeight(50)
        self.export_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #E06C75;
            }
            QPushButton:hover {
                background-color: #D55A65;
            }
        """)
        self.export_btn.clicked.connect(self.export_video)
        self.export_btn.setEnabled(False)  # 初始禁用

        # 添加按钮到布局（等宽分布）
        export_layout.addWidget(self.text_edit_btn_export, 1)
        export_layout.addWidget(self.export_settings_btn_export, 1)
        export_layout.addWidget(self.export_btn, 1)

        layout.addLayout(export_layout)

    def _create_batch_group(self) -> QGroupBox:
        """创建批量处理组"""
        batch_group = QGroupBox("批量处理")
        batch_layout = QVBoxLayout(batch_group)
        batch_layout.setContentsMargins(10, 15, 10, 10)
        batch_layout.setSpacing(8)

        # 批量处理开关行（水平布局）
        batch_header_layout = QHBoxLayout()

        self.batch_mode_checkbox = QCheckBox("▲ 启用批量处理模式")
        self.batch_mode_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ABB2BF;
                font-size: 14px;
            }
            QCheckBox::indicator {
                width: 0px;
                height: 0px;
            }
        """)
        self.batch_mode_checkbox.toggled.connect(self.toggle_batch_mode)
        batch_header_layout.addWidget(self.batch_mode_checkbox)

        # 添加弹性空间
        batch_header_layout.addStretch()

        # 重置处理记录按钮（最简单样式）
        self.reset_batch_btn = QPushButton("重置记录")
        self.reset_batch_btn.setFixedHeight(24)
        self.reset_batch_btn.setFixedWidth(80)
        self.reset_batch_btn.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 4px;
                font-size: 12px;
                padding: 2px 6px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border-color: #999999;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        self.reset_batch_btn.clicked.connect(self.reset_batch_processing)
        batch_header_layout.addWidget(self.reset_batch_btn)

        batch_layout.addLayout(batch_header_layout)

        # 批量处理设置（初始隐藏）
        self.batch_settings_widget = self._create_batch_settings()
        self.batch_settings_widget.hide()  # 初始隐藏
        batch_layout.addWidget(self.batch_settings_widget)

        return batch_group

    def _create_batch_settings(self) -> QWidget:
        """创建批量处理设置"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 监控文件夹
        folder_layout = QHBoxLayout()
        folder_layout.addWidget(QLabel("监控文件夹:"))
        self.watch_folder_edit = QLineEdit()
        self.watch_folder_edit.setPlaceholderText("选择要监控的文件夹...")
        self.watch_folder_edit.setReadOnly(True)
        folder_layout.addWidget(self.watch_folder_edit)

        self.browse_watch_folder_btn = QPushButton("浏览")
        self.browse_watch_folder_btn.setFixedWidth(60)
        self.browse_watch_folder_btn.clicked.connect(self.browse_watch_folder)
        folder_layout.addWidget(self.browse_watch_folder_btn)

        layout.addLayout(folder_layout)

        # 输出文件夹
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出文件夹:"))
        self.output_folder_edit = QLineEdit()
        self.output_folder_edit.setPlaceholderText("选择输出文件夹...")
        self.output_folder_edit.setReadOnly(True)
        output_layout.addWidget(self.output_folder_edit)

        self.browse_output_folder_btn = QPushButton("浏览")
        self.browse_output_folder_btn.setFixedWidth(60)
        self.browse_output_folder_btn.clicked.connect(self.browse_output_folder)
        output_layout.addWidget(self.browse_output_folder_btn)

        layout.addLayout(output_layout)

        # 批量处理状态和控制
        status_layout = QHBoxLayout()

        self.batch_status_label = QLabel("状态: 未启动")
        self.batch_status_label.setStyleSheet("color: #98C379; font-size: 12px;")
        status_layout.addWidget(self.batch_status_label)

        # 批量处理启动勾选框
        self.batch_enable_checkbox = QCheckBox("启动批量处理")
        self.batch_enable_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ABB2BF;
                font-size: 12px;
                margin-left: 20px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #5C6370;
                background-color: transparent;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #98C379;
                background-color: #98C379;
                border-radius: 3px;
            }
        """)
        self.batch_enable_checkbox.toggled.connect(self.toggle_batch_processing)
        status_layout.addWidget(self.batch_enable_checkbox)
        status_layout.addStretch()

        layout.addLayout(status_layout)



        # 日志显示区域（默认隐藏）
        self.batch_log_display = QTextEdit()
        self.batch_log_display.setMaximumHeight(80)  # 缩小高度从120到80
        self.batch_log_display.setReadOnly(True)
        self.batch_log_display.setVisible(False)  # 默认隐藏
        self.batch_log_display.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #ABB2BF;
                border: 1px solid #5C6370;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                padding: 5px;
            }
            QScrollBar:vertical {
                background-color: #2C2C2C;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #5C6370;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #61AFEF;
            }
        """)
        layout.addWidget(self.batch_log_display)

        return widget

    def set_cover_preview_aspect_ratio(self):
        """根据视频分辨率设置封面预览区的宽高比"""
        if hasattr(self, 'video_width') and hasattr(self, 'video_height'):
            # 更新PreviewLabel的宽高比
            if hasattr(self.cover_preview, 'aspect_ratio'):
                self.cover_preview.aspect_ratio = self.video_aspect_ratio

    def load_matching_cover_by_aspect_ratio(self):
        """根据视频宽高比自动加载匹配的Cover图片"""
        try:
            # 检查是否有有效的视频信息
            if not hasattr(self, 'video_aspect_ratio') or not hasattr(self, 'video_width') or not hasattr(self, 'video_height'):
                print("没有视频信息，跳过自动匹配封面")
                return

            # Cover文件夹路径
            cover_folder = os.path.join(os.path.dirname(os.path.dirname(__file__)), "Cover")

            if not os.path.exists(cover_folder):
                print("Cover文件夹不存在")
                return

            # 支持的图片格式
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff']

            # 获取当前视频的宽高比
            current_aspect_ratio = self.video_aspect_ratio

            # 定义宽高比类型
            def get_aspect_ratio_type(aspect_ratio):
                if aspect_ratio > 1.5:  # 横屏 (16:9 ≈ 1.78, 4:3 ≈ 1.33)
                    return "landscape"
                elif aspect_ratio < 0.75:  # 竖屏 (9:16 ≈ 0.56, 3:4 ≈ 0.75)
                    return "portrait"
                else:  # 方形或接近方形
                    return "square"

            current_type = get_aspect_ratio_type(current_aspect_ratio)

            # 收集所有图片及其宽高比信息
            image_candidates = []
            for filename in os.listdir(cover_folder):
                if any(filename.lower().endswith(ext) for ext in image_extensions):
                    image_path = os.path.join(cover_folder, filename)
                    pixmap = QPixmap(image_path)

                    if not pixmap.isNull():
                        img_aspect_ratio = pixmap.width() / pixmap.height()
                        img_type = get_aspect_ratio_type(img_aspect_ratio)

                        # 计算与目标宽高比的差异
                        aspect_diff = abs(img_aspect_ratio - current_aspect_ratio)

                        image_candidates.append({
                            'path': image_path,
                            'filename': filename,
                            'pixmap': pixmap,
                            'aspect_ratio': img_aspect_ratio,
                            'type': img_type,
                            'aspect_diff': aspect_diff
                        })

            if not image_candidates:
                return

            # 优先选择相同类型的图片，然后按宽高比差异排序
            same_type_candidates = [img for img in image_candidates if img['type'] == current_type]

            if same_type_candidates:
                # 选择相同类型中宽高比最接近的
                best_match = min(same_type_candidates, key=lambda x: x['aspect_diff'])
            else:
                # 如果没有相同类型，选择宽高比最接近的
                best_match = min(image_candidates, key=lambda x: x['aspect_diff'])

            # 加载最匹配的图片
            original_pixmap = best_match['pixmap']

            # 缩放图片到视频分辨率
            scaled_pixmap = self._scale_to_video_resolution(original_pixmap)

            # 清除现有图层
            self.cover_layers.clear()
            self.current_layer_index = -1

            # 添加缩放后的封面作为图层
            self.original_image = scaled_pixmap
            self.add_cover_layer(
                scaled_pixmap,
                layer_type="auto_matched",
                source_info=f"自动匹配: {best_match['filename']} ({current_type}) - 缩放至 {self.video_width}x{self.video_height}"
            )

            print(f"自动加载匹配封面: {best_match['filename']} (类型: {current_type}, 原始: {original_pixmap.width()}x{original_pixmap.height()}, 缩放至: {self.video_width}x{self.video_height})")

            # 更新预览区显示
            self.update_composite_cover()

        except Exception as e:
            print(f"自动加载匹配封面失败: {e}")

    def _scale_to_video_resolution(self, pixmap: QPixmap) -> QPixmap:
        """将图片缩放到视频分辨率，保持宽高比"""
        try:
            if not pixmap or pixmap.isNull():
                return pixmap

            if not hasattr(self, 'video_width') or not hasattr(self, 'video_height'):
                print("没有视频分辨率信息，返回原始图片")
                return pixmap

            # 缩放到视频分辨率，保持宽高比
            scaled_pixmap = pixmap.scaled(
                self.video_width,
                self.video_height,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            return scaled_pixmap

        except Exception as e:
            print(f"缩放图片到视频分辨率失败: {e}")
            return pixmap

    def add_cover_layer(self, pixmap, layer_type="manual", source_info=""):
        """添加封面图层"""
        try:
            if pixmap and not pixmap.isNull():
                layer_info = {
                    'id': len(self.cover_layers),
                    'pixmap': pixmap,
                    'type': layer_type,
                    'source': source_info,
                    'visible': True,
                    'opacity': 1.0
                }
                self.cover_layers.append(layer_info)
                self.current_layer_index = len(self.cover_layers) - 1
                print(f"添加图层: {source_info} (总图层数: {len(self.cover_layers)})")

                # 更新封面预览显示
                self.update_composite_cover()
        except Exception as e:
            print(f"添加图层失败: {e}")
            import traceback
            traceback.print_exc()

    def toggle_batch_log_display(self):
        """切换批量处理日志显示/隐藏（Ctrl+1快捷键）"""
        try:
            print("📋 Ctrl+1 快捷键被触发")

            # 检查批量处理面板是否存在
            if not hasattr(self, 'batch_log_display'):
                print("📋 批量处理日志面板不存在，尝试创建...")
                # 如果日志面板不存在，可能需要先启用批量处理
                if hasattr(self, 'batch_mode_checkbox'):
                    self.batch_mode_checkbox.setChecked(True)
                    self.toggle_batch_mode(True)
                return

            # 检查批量处理面板是否显示
            if not hasattr(self, 'batch_settings_shown') or not self.batch_settings_shown:
                print("📋 批量处理面板未显示，自动启用批量处理模式")
                # 自动启用批量处理模式
                if hasattr(self, 'batch_mode_checkbox'):
                    self.batch_mode_checkbox.setChecked(True)
                    self.toggle_batch_mode(True)
                # 延迟切换日志显示
                QTimer.singleShot(100, self.toggle_batch_log_display)
                return

            # 切换日志显示状态
            self.batch_log_visible = not self.batch_log_visible
            self.batch_log_display.setVisible(self.batch_log_visible)

            if self.batch_log_visible:
                print("📋 批量处理日志已显示")
                self.add_batch_log("📋 日志面板已显示 (Ctrl+1 隐藏)", "info")
            else:
                print("📋 批量处理日志已隐藏")

        except Exception as e:
            print(f"切换日志显示失败: {e}")
            import traceback
            traceback.print_exc()

    def clear_batch_log(self):
        """清空批量处理日志"""
        if hasattr(self, 'batch_log_display'):
            self.batch_log_display.clear()
            self.add_batch_log("日志已清空", "info")

    def add_batch_log(self, message: str, level: str = "info"):
        """添加批量处理日志"""
        if not hasattr(self, 'batch_log_display'):
            return

        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据日志级别设置颜色
        color_map = {
            "info": "#ABB2BF",      # 普通信息 - 灰色
            "success": "#98C379",   # 成功 - 绿色
            "warning": "#E5C07B",   # 警告 - 黄色
            "error": "#E06C75",     # 错误 - 红色
            "processing": "#61AFEF" # 处理中 - 蓝色
        }

        color = color_map.get(level, "#ABB2BF")
        formatted_message = f'<span style="color: {color}">[{timestamp}] {message}</span>'

        # 添加到日志显示
        self.batch_log_display.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.batch_log_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # 限制日志行数，避免内存占用过多
        document = self.batch_log_display.document()
        if document.blockCount() > 200:  # 最多保留200行
            cursor = self.batch_log_display.textCursor()
            cursor.movePosition(cursor.MoveOperation.Start)
            cursor.select(cursor.SelectionType.BlockUnderCursor)
            cursor.removeSelectedText()

    def _get_base_style(self) -> str:
        """获取基础样式"""
        return """
            QWidget {
                background-color: #181A1F;
                color: #D0D0D0;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #303238;
                border-radius: 6px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLabel {
                color: #ABB2BF;
            }
            QLineEdit {
                background-color: #2C2E34;
                border: 1px solid #5C6370;
                border-radius: 4px;
                padding: 5px;
                color: #D0D0D0;
            }
            QLineEdit:focus {
                border-color: #61AFEF;
            }
            QPushButton {
                background-color: #3A3F4B;
                color: #ABB2BF;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4A4F5B;
            }
            QPushButton:pressed {
                background-color: #2A2F3B;
            }
        """

    def _setup_media_player(self):
        """设置媒体播放器"""
        try:
            # 初始化媒体播放器
            self.media_player = QMediaPlayer()
            self.audio_output = QAudioOutput()
            self.media_player.setAudioOutput(self.audio_output)
            self.media_player.setVideoOutput(self.video_widget)

            # 设置初始音量
            self.audio_output.setVolume(0.7)  # 中等音量

            # 连接媒体播放器信号
            self.media_player.positionChanged.connect(self.update_playback_position)
            self.media_player.durationChanged.connect(self.update_playback_duration)
            self.media_player.errorOccurred.connect(self.handle_media_error)
            self.media_player.playbackStateChanged.connect(self.on_playback_state_changed)

            # 连接控制信号
            if hasattr(self, 'playback_slider'):
                self.playback_slider.sliderMoved.connect(self.set_playback_position)
                self.playback_slider.sliderPressed.connect(self.on_slider_pressed)
                self.playback_slider.sliderReleased.connect(self.on_slider_released)

        except Exception as e:
            print(f"设置媒体播放器失败: {e}")



    def _load_default_cover(self):
        """加载默认封面"""
        try:
            # 查找Cover文件夹中的默认封面
            cover_folder = os.path.join(os.path.dirname(os.path.dirname(__file__)), "Cover")
            if not os.path.exists(cover_folder):
                return

            # 查找第一张图片
            for filename in os.listdir(cover_folder):
                if any(filename.lower().endswith(ext) for ext in Constants.IMAGE_EXTENSIONS):
                    image_path = os.path.join(cover_folder, filename)

                    # 加载图片
                    pixmap = QPixmap(image_path)
                    if not pixmap.isNull():
                        # 根据默认图片的实际比例设置预览区比例
                        image_aspect_ratio = pixmap.width() / pixmap.height()
                        self.video_aspect_ratio = image_aspect_ratio
                        self.video_width = pixmap.width()
                        self.video_height = pixmap.height()

                        # 添加默认封面作为图层
                        self.original_image = pixmap
                        self.add_cover_layer(
                            pixmap,
                            layer_type="imported",
                            source_info=f"默认封面: {filename}"
                        )

                        # 确保预览区能正确显示图像
                        QTimer.singleShot(100, self.refresh_preview_display)
                        break

        except Exception as e:
            # 如果加载失败，静默处理，不影响程序启动
            print(f"加载默认封面失败: {e}")

    # =============================================================
    # 事件处理方法（占位符，需要具体实现）
    # =============================================================

    def switch_to_play_mode(self):
        """切换到播放模式"""
        if self.play_mode_btn.isChecked():
            # 确保截图模式按钮取消选中
            self.snapshot_mode_btn.setChecked(False)

            # 切换到播放模式预览
            self.preview_stack.setCurrentIndex(1)
            self.control_stack.setCurrentIndex(0)  # 显示播放控制面板

            # 加载视频到播放器
            if self.video_path:
                self.load_media_player(self.video_path)

                # 设置播放位置到裁剪开始位置
                if self.video_cap and self.video_cap.isOpened():
                    start_frames = getattr(self, 'cropped_start_frames', 0)
                    start_time = start_frames / self.fps if self.fps > 0 else 0
                    self.media_player.setPosition(int(start_time * 1000))

                    # 更新媒体播放器的播放范围
                    self.update_media_player_range()

            # 确保音频输出已启用
            if self.audio_output:
                self.audio_output.setMuted(False)  # 取消静音

    def switch_to_snapshot_mode(self):
        """切换到截图模式"""
        self.preview_stack.setCurrentIndex(0)
        self.control_stack.setCurrentIndex(1)

    def capture_current_frame(self):
        """截取当前帧"""
        try:
            if not self.video_cap or not self.video_cap.isOpened():
                QMessageBox.warning(self, Constants.MSG_WARNING, "请先加载视频文件")
                return

            # 获取当前帧位置
            current_frame_pos = self.current_frame

            # 如果在播放模式，从媒体播放器获取当前位置
            if self.play_mode_btn.isChecked() and self.media_player.duration() > 0:
                current_time_ms = self.media_player.position()
                current_time_s = current_time_ms / 1000.0
                if hasattr(self, 'fps') and self.fps > 0:
                    current_frame_pos = int(current_time_s * self.fps)

            # 获取当前帧
            self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, current_frame_pos)
            ret, frame = self.video_cap.read()

            if ret:
                # 转换BGR到RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 转换为QImage和QPixmap
                height, width, _ = frame_rgb.shape
                bytes_per_line = 3 * width
                q_image = QImage(frame_rgb.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)

                # 添加为封面图层
                self.add_cover_layer(
                    pixmap,
                    layer_type="captured",
                    source_info=f"截取帧: {current_frame_pos}"
                )

                QMessageBox.information(self, Constants.MSG_SUCCESS, f"成功截取第 {current_frame_pos} 帧")

            else:
                QMessageBox.warning(self, Constants.MSG_WARNING, "无法读取当前帧")

        except Exception as e:
            QMessageBox.critical(self, Constants.MSG_ERROR, f"截取帧失败: {str(e)}")

    def smart_capture_frame(self):
        """智能截取帧"""
        try:
            if not self.video_cap or not self.video_cap.isOpened():
                QMessageBox.warning(self, Constants.MSG_WARNING, "请先加载视频文件")
                return

            # 简单的智能截取：选择视频中间位置的帧
            middle_frame = self.total_frames // 2

            # 避开开头和结尾的裁剪区域
            start_frame = self.cropped_start_frames
            end_frame = self.total_frames - self.cropped_end_frames

            if end_frame > start_frame:
                smart_frame = start_frame + (end_frame - start_frame) // 2
            else:
                smart_frame = middle_frame

            # 设置滑块位置并截取
            self.frame_slider.setValue(smart_frame)
            self.show_frame(smart_frame)

            # 截取当前帧
            self.capture_current_frame()

        except Exception as e:
            QMessageBox.critical(self, Constants.MSG_ERROR, f"智能截取失败: {str(e)}")

    def import_cover_image(self):
        """导入封面图片"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择封面图片",
                self.last_image_path,
                "图片文件 (*.jpg *.jpeg *.png *.bmp *.gif *.tiff);;所有文件 (*)"
            )

            if file_path:
                self.last_image_path = os.path.dirname(file_path)
                set_path('cover_edit_image', file_path)  # 保存路径

                # 加载图片
                original_pixmap = QPixmap(file_path)
                if not original_pixmap.isNull():
                    # 缩放图片到视频分辨率
                    scaled_pixmap = self._scale_to_video_resolution(original_pixmap)

                    # 添加为封面图层
                    if hasattr(self, 'video_width') and hasattr(self, 'video_height'):
                        source_info = f"导入图片: {os.path.basename(file_path)} - 缩放至 {self.video_width}x{self.video_height}"
                        print(f"导入封面图片: {os.path.basename(file_path)} (原始: {original_pixmap.width()}x{original_pixmap.height()}, 缩放至: {self.video_width}x{self.video_height})")
                    else:
                        source_info = f"导入图片: {os.path.basename(file_path)}"
                        print(f"导入封面图片: {os.path.basename(file_path)} (原始: {original_pixmap.width()}x{original_pixmap.height()})")

                    self.add_cover_layer(
                        scaled_pixmap,
                        layer_type="imported",
                        source_info=source_info
                    )
                    QMessageBox.information(self, Constants.MSG_SUCCESS, "成功导入封面图片")
                else:
                    QMessageBox.warning(self, Constants.MSG_WARNING, "无法加载选择的图片文件")

        except Exception as e:
            QMessageBox.critical(self, Constants.MSG_ERROR, f"导入封面失败: {str(e)}")

    def toggle_playback(self):
        """切换播放/暂停状态"""
        try:
            if not self.media_player.source().isEmpty():
                from PyQt6.QtMultimedia import QMediaPlayer

                if self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState:
                    # 当前正在播放，暂停
                    self.media_player.pause()
                    self.play_pause_btn.setText("播放")
                else:
                    # 当前暂停或停止，开始播放
                    self.media_player.play()
                    self.play_pause_btn.setText("暂停")
            else:
                QMessageBox.warning(self, Constants.MSG_WARNING, "请先加载视频文件")
        except Exception as e:
            print(f"播放控制错误: {e}")
            QMessageBox.warning(self, Constants.MSG_ERROR, f"播放控制失败: {str(e)}")

    def on_frame_slider_changed(self, value):
        """帧滑块变化处理"""
        if self.video_cap and self.video_cap.isOpened():
            self.show_frame(value)

    def browse_video_file(self):
        """浏览视频文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择视频文件",
                self.last_video_path,
                "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.ts *.mts *.m2ts);;所有文件 (*)"
            )

            if file_path:
                self.last_video_path = os.path.dirname(file_path)
                set_path('cover_edit_video', file_path)  # 保存路径
                self.load_video(file_path)

        except Exception as e:
            QMessageBox.critical(self, Constants.MSG_ERROR, f"选择视频文件失败: {str(e)}")

    def load_video(self, file_path: str):
        """加载视频文件"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"视频文件不存在: {file_path}")

            # 释放之前的视频资源
            if self.video_cap:
                self.video_cap.release()

            # 打开视频文件
            self.video_cap = cv2.VideoCapture(file_path)
            if not self.video_cap.isOpened():
                # 尝试使用不同的后端
                print(f"默认后端无法打开视频，尝试其他后端...")
                self.video_cap = cv2.VideoCapture(file_path, cv2.CAP_FFMPEG)
                if not self.video_cap.isOpened():
                    raise Exception("无法打开视频文件（已尝试多种后端）")

            # 获取视频信息
            self.video_path = file_path
            self.total_frames = int(self.video_cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.video_cap.get(cv2.CAP_PROP_FPS)
            self.duration = self.total_frames / self.fps if self.fps > 0 else 0
            self.video_width = int(self.video_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.video_height = int(self.video_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.video_aspect_ratio = self.video_width / self.video_height

            # 添加详细的视频信息日志
            print(f"视频加载成功: {os.path.basename(file_path)}")
            print(f"  - 分辨率: {self.video_width}x{self.video_height}")
            print(f"  - 帧数: {self.total_frames}")
            print(f"  - 帧率: {self.fps:.2f}")
            print(f"  - 时长: {self.duration:.1f}秒 ({self.duration/60:.1f}分钟)")

            # 检查是否有异常的视频参数
            if self.duration > 180:  # 超过3分钟
                print(f"  - 注意: 视频时长超过3分钟 ({self.duration:.1f}秒)")
            if self.total_frames <= 0 or self.fps <= 0:
                print(f"  - 警告: 视频参数异常 - 帧数:{self.total_frames}, 帧率:{self.fps}")

            # 更新UI显示
            self.video_path_edit.setText(os.path.basename(file_path))
            self.resolution_label.setText(f"{self.video_width}x{self.video_height}")
            self.duration_label.setText(f"{self.duration:.1f}秒")

            # 设置滑块范围
            self.frame_slider.setRange(0, self.total_frames - 1)

            # 设置封面预览区的比例
            self.set_cover_preview_aspect_ratio()

            # 根据视频比例自动加载匹配的Cover图片
            self.load_matching_cover_by_aspect_ratio()

            # 设置默认裁剪值并更新UI
            self.cropped_start_frames = Constants.DEFAULT_CROP_START
            self.cropped_end_frames = Constants.DEFAULT_CROP_END

            # 🔧 修复：更新输入框显示默认值
            self.crop_start_input.setText(str(Constants.DEFAULT_CROP_START))
            self.crop_end_input.setText(str(Constants.DEFAULT_CROP_END))
            print(f"设置默认裁剪值: 开头{Constants.DEFAULT_CROP_START}帧, 结尾{Constants.DEFAULT_CROP_END}帧")

            # 确保切换到截图模式
            self.switch_to_snapshot_mode()

            # 显示第一帧
            self.frame_slider.setValue(0)
            self.current_frame = 0
            self.show_frame(0)

            # 更新时长标签
            self.update_duration_label()

            # 加载到媒体播放器
            self.load_media_player(file_path)

            # 启用导出按钮
            self.export_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, Constants.MSG_ERROR, f"加载视频时出错: {str(e)}")

    def show_frame(self, frame_number: int):
        """显示指定帧"""
        try:
            if not self.video_cap or not self.video_cap.isOpened():
                return

            # 设置视频位置到指定帧
            self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = self.video_cap.read()

            if ret:
                # 转换BGR到RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 转换为QImage
                height, width, _ = frame_rgb.shape
                bytes_per_line = 3 * width
                q_image = QImage(frame_rgb.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)

                # 转换为QPixmap并显示
                pixmap = QPixmap.fromImage(q_image)
                self.snapshot_preview.set_pixmap(pixmap)

                # 更新当前帧信息
                self.current_frame = frame_number
                current_time = frame_number / self.fps if self.fps > 0 else 0
                total_time = self.duration

                # 更新帧标签
                self.frame_label.setText(f"帧: {frame_number}/{self.total_frames}")

                # 更新时间标签
                if hasattr(self, 'time_label'):
                    self.time_label.setText(f"{self.format_time(current_time)} / {self.format_time(total_time)}")

        except Exception as e:
            print(f"显示帧时出错: {e}")

    def load_media_player(self, file_path: str):
        """加载媒体播放器"""
        try:
            if self.media_player:
                url = QUrl.fromLocalFile(file_path)
                self.media_player.setSource(url)

                # 更新播放范围
                if hasattr(self, 'cropped_start_frames') and hasattr(self, 'cropped_end_frames'):
                    self.update_media_player_range()

                # 设置到第一帧位置（避免黑屏）
                if hasattr(self, 'fps') and self.fps > 0:
                    # 计算第一帧的时间（毫秒）
                    start_frame = getattr(self, 'cropped_start_frames', 0)
                    first_frame_time_ms = int((start_frame / self.fps) * 1000)

                    # 设置播放器位置到第一帧
                    self.media_player.setPosition(first_frame_time_ms)

                    # 更新位置滑块
                    if hasattr(self, 'playback_slider'):
                        self.playback_slider.setValue(first_frame_time_ms)
        except Exception as e:
            print(f"加载媒体播放器失败: {e}")

    def format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def update_duration_label(self):
        """更新时长标签"""
        try:
            # 计算裁剪后的时长
            start_frame = self.cropped_start_frames
            end_frame = self.total_frames - self.cropped_end_frames

            if end_frame > start_frame:
                cropped_duration = (end_frame - start_frame) / self.fps if self.fps > 0 else 0
                self.duration_label.setText(f"裁剪后时长: {self.format_time(cropped_duration)}")
            else:
                self.duration_label.setText("裁剪后时长: 无效")

        except Exception as e:
            print(f"更新时长标签失败: {e}")

    def on_crop_settings_changed(self):
        """裁剪设置变化处理"""
        try:
            # 获取裁剪设置
            start_text = self.crop_start_input.text().strip()
            end_text = self.crop_end_input.text().strip()

            # 更新裁剪帧数 - 空值时默认为0（不裁剪）
            old_start_frames = getattr(self, 'cropped_start_frames', Constants.DEFAULT_CROP_START)
            old_end_frames = getattr(self, 'cropped_end_frames', Constants.DEFAULT_CROP_END)

            # 处理输入值：空值或非数字时默认为0（不裁剪）
            if start_text == '' or not start_text.isdigit():
                new_start_frames = 0
            else:
                new_start_frames = int(start_text)

            if end_text == '' or not end_text.isdigit():
                new_end_frames = 0
            else:
                new_end_frames = int(end_text)

            # 检查是否有实际变化
            start_changed = old_start_frames != new_start_frames
            end_changed = old_end_frames != new_end_frames

            # 更新值
            self.cropped_start_frames = new_start_frames
            self.cropped_end_frames = new_end_frames

            print(f"裁剪设置更新: 开头 {old_start_frames}->{new_start_frames}, 结尾 {old_end_frames}->{new_end_frames}")

            # 更新时长显示
            self.update_duration_label()

            # 如果在播放模式，更新媒体播放器
            if self.play_mode_btn.isChecked() and hasattr(self, 'media_player') and self.media_player:
                # 更新播放范围
                self.update_media_player_range()

                # 如果开头裁剪帧数发生变化，跳转到新的开始位置
                if start_changed:
                    start_time_ms = int((self.cropped_start_frames / self.fps) * 1000) if self.fps > 0 else 0
                    self.media_player.setPosition(start_time_ms)

                    # 🔧 修复：同时更新播放滑块位置
                    if hasattr(self, 'playback_slider'):
                        self.playback_slider.setValue(start_time_ms)

                    # 特殊处理：当裁剪值为0时，确保跳转到真正的第一帧
                    if self.cropped_start_frames == 0:
                        print(f"🎯 跳转到原始视频第一帧: 0ms (第0帧)")
                    else:
                        print(f"🎯 跳转到新的开始位置: {start_time_ms}ms (第{self.cropped_start_frames}帧)")

                # 如果结尾裁剪帧数发生变化，也提供反馈
                if end_changed:
                    print(f"📏 更新结尾裁剪: 第{self.total_frames - self.cropped_end_frames}帧结束")

            # 如果在截图模式，更新帧滑块的有效范围显示
            elif self.snapshot_mode_btn.isChecked():
                # 更新帧滑块的样式或提示，显示裁剪范围
                if start_changed or end_changed:
                    self._update_frame_slider_range_indicator()

                    # 🔧 修复：如果开头裁剪变为0，跳转到第一帧
                    if start_changed and self.cropped_start_frames == 0:
                        self.frame_slider.setValue(0)
                        self.current_frame = 0
                        self.show_frame(0)
                        print(f"🎯 截图模式跳转到原始视频第一帧: 第0帧")

        except Exception as e:
            print(f"更新裁剪设置失败: {e}")

    def _update_frame_slider_range_indicator(self):
        """更新帧滑块的裁剪范围指示器"""
        try:
            if not hasattr(self, 'frame_slider') or not self.frame_slider:
                return

            # 计算有效范围
            start_frame = max(0, self.cropped_start_frames)
            end_frame = max(start_frame + 1, self.total_frames - self.cropped_end_frames)

            # 确保范围有效
            if end_frame <= start_frame:
                end_frame = start_frame + 1

            # 更新滑块的工具提示
            if self.cropped_start_frames == 0 and self.cropped_end_frames == 0:
                tooltip = "无裁剪 - 完整视频"
            else:
                tooltip = f"有效范围: 第{start_frame}帧 - 第{end_frame-1}帧"
            self.frame_slider.setToolTip(tooltip)

            # 如果当前帧在裁剪范围外，跳转到范围内
            current_frame = self.frame_slider.value()
            jumped = False

            if current_frame < start_frame:
                self.frame_slider.setValue(start_frame)
                self.show_frame(start_frame)
                print(f"🎯 截图模式跳转: 当前帧({current_frame}) -> 开始帧({start_frame})")
                jumped = True
            elif current_frame >= end_frame:
                target_frame = max(start_frame, end_frame - 1)
                self.frame_slider.setValue(target_frame)
                self.show_frame(target_frame)
                print(f"🎯 截图模式跳转: 当前帧({current_frame}) -> 结束帧({target_frame})")
                jumped = True

            if not jumped:
                print(f"📍 截图模式范围更新: 第{start_frame}-{end_frame-1}帧，当前第{current_frame}帧")

        except Exception as e:
            print(f"更新帧滑块范围指示器失败: {e}")

    def open_text_properties(self):
        """打开文字属性窗口并激活文字输入功能"""
        try:
            # 导入并创建文字属性窗口
            from modules.text_properties_window import TextPropertiesWindow

            # 如果窗口不存在，创建新窗口
            if not hasattr(self, 'text_properties_window') or self.text_properties_window is None:
                self.text_properties_window = TextPropertiesWindow(self)
                # 连接信号
                self._connect_text_property_signals()
            else:
                # 窗口已存在，确保信号连接正常（避免重复连接）
                print("文字属性窗口已存在，跳过信号连接")

            # 显示窗口
            self.text_properties_window.show()
            self.text_properties_window.raise_()
            self.text_properties_window.activateWindow()

            # 激活文字输入功能
            self._activate_text_input_mode()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开文字属性窗口失败: {str(e)}")

    def _connect_text_property_signals(self):
        """连接文字属性窗口的信号"""
        try:
            window = self.text_properties_window

            # 字体信号连接（2条）
            window.font_changed_unselected.connect(self._on_font_changed_unselected)
            window.font_changed_selected.connect(self._on_font_changed_selected)

            # 样式信号连接（2条）
            window.font_style_changed_unselected.connect(self._on_font_style_changed_unselected)
            window.font_style_changed_selected.connect(self._on_font_style_changed_selected)

            # 大小信号连接（2条）
            window.font_size_changed_unselected.connect(self._on_font_size_changed_unselected)
            window.font_size_changed_selected.connect(self._on_font_size_changed_selected)

            # 填充颜色信号连接（2条）
            window.fill_color_changed_unselected.connect(self._on_fill_color_changed_unselected)
            window.fill_color_changed_selected.connect(self._on_fill_color_changed_selected)

            # 描边信号连接（2条）
            window.stroke_color_changed_unselected.connect(self._on_stroke_changed_unselected)
            window.stroke_color_changed_selected.connect(self._on_stroke_changed_selected)

            # 全局属性信号连接（1条）
            window.background_changed.connect(self._on_background_changed)
            window.spacing_changed.connect(self._on_spacing_changed)

            print("文字属性信号连接完成")

        except Exception as e:
            print(f"连接文字属性信号失败: {e}")

    def _activate_text_input_mode(self):
        """激活文字输入模式"""
        try:
            # 设置文字输入模式标志
            self.text_input_mode = True

            # 为封面预览添加鼠标事件处理
            self.cover_preview.mousePressEvent = self._on_cover_mouse_press

        except Exception as e:
            print(f"激活文字输入模式失败: {e}")

    def _on_cover_mouse_press(self, event):
        """封面预览鼠标点击事件"""
        try:
            if not hasattr(self, 'text_input_mode') or not self.text_input_mode:
                return

            # 只处理左键点击
            if event.button() == Qt.MouseButton.LeftButton:
                # 获取点击位置
                click_pos = event.pos()

                # 创建文本输入框
                self._create_text_input_box(click_pos)

        except Exception as e:
            print(f"处理封面鼠标点击失败: {e}")

    def _create_text_input_box(self, position):
        """创建简单的富文本输入框"""
        try:
            from PyQt6.QtWidgets import QTextEdit
            from PyQt6.QtCore import Qt
            from PyQt6.QtGui import QFont

            # 创建富文本输入框
            text_input = QTextEdit(self.cover_preview)

            # 简单样式
            text_input.setStyleSheet("""
                QTextEdit {
                    background-color: transparent;
                    border: 1px solid white;
                    color: red;
                    font-size: 24px;
                    font-family: '微软雅黑';
                    font-weight: bold;
                    padding: 5px;
                    selection-background-color: rgba(0, 120, 215, 100);
                    selection-color: white;
                }
            """)

            # 设置位置和大小
            text_input.move(position.x(), position.y())
            text_input.resize(200, 50)

            # 基本设置
            text_input.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
            text_input.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
            text_input.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)  # 禁用自动换行

            # 屏蔽右键菜单
            text_input.setContextMenuPolicy(Qt.ContextMenuPolicy.NoContextMenu)

            # 连接文本变化事件（使用partial避免lambda闭包问题）
            from functools import partial
            text_input.textChanged.connect(partial(self._on_text_changed, text_input))

            # 连接鼠标事件以支持右键拖动
            text_input.mousePressEvent = lambda event: self._text_input_mouse_press(event, text_input)
            text_input.mouseMoveEvent = lambda event: self._text_input_mouse_move(event, text_input)
            text_input.mouseReleaseEvent = lambda event: self._text_input_mouse_release(event, text_input)

            # 连接键盘事件以处理删除键
            text_input.keyPressEvent = lambda event: self._text_input_key_press(event, text_input)

            # 连接滚轮事件以禁用滚动
            text_input.wheelEvent = lambda event: self._text_input_wheel_event(event, text_input)

            # 连接光标位置变化事件，确保新输入文字继承正确格式（使用partial避免lambda闭包问题）
            text_input.cursorPositionChanged.connect(partial(self._on_cursor_position_changed, text_input))

            # 确保输入框在最上层
            text_input.raise_()
            text_input.show()
            text_input.setFocus()

            # 确保输入框在最上层
            text_input.raise_()
            text_input.show()
            text_input.setFocus()

            # 创建后立即调整一次大小（即使是空内容也要设置正确的初始大小）
            self._adjust_text_input_size(text_input)

            # 不设置初始文字，让用户直接输入

            # 保存文本输入框引用
            if not hasattr(self, 'text_inputs'):
                self.text_inputs = []
            self.text_inputs.append(text_input)

            print(f"✅ 创建文本输入框在位置: {position.x()}, {position.y()}")

        except Exception as e:
            print(f"❌ 创建文本输入框失败: {e}")

    def _on_text_changed(self, text_input):
        """处理文字变化事件"""
        try:
            # 获取文本内容（保留所有空白和换行）
            text_content = text_input.toPlainText()
            text_content_stripped = text_content.strip()

            print(f"📝 文字变化检测: 原始长度={len(text_content)}, 去空白后长度={len(text_content_stripped)}")
            print(f"📝 原始内容: '{text_content}', 去空白内容: '{text_content_stripped}'")

            # 如果文本为空，使用延迟删除确保稳定性
            if not text_content_stripped:
                print(f"🗑️ 检测到空文本，准备延迟删除输入框: {text_input}")
                # 使用短延迟确保文本变化事件完全处理完毕
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(50, lambda: self._delayed_remove_if_empty(text_input))
                return
            else:
                # 只在有换行时才检查格式问题
                if '\n' in text_content:
                    # 使用延迟修复，避免干扰正在进行的编辑
                    from PyQt6.QtCore import QTimer
                    QTimer.singleShot(100, lambda: self._fix_text_format_issues(text_input))

                # 智能大小调整：减少不必要的调整但确保正常显示
                lines_count = len(text_content.split('\n'))
                should_adjust_size = False

                # 检查是否需要调整大小
                if hasattr(text_input, '_last_content_length'):
                    length_change = abs(len(text_content) - text_input._last_content_length)
                    # 降低阈值，确保单行文字也能正常调整
                    if length_change > 1 or '\n' in text_content or lines_count > 1:
                        should_adjust_size = True
                    print(f"🔍 大小调整检查: 长度变化={length_change}, 行数={lines_count}, 需要调整={should_adjust_size}")
                else:
                    should_adjust_size = True  # 首次设置
                    print(f"🔍 首次设置，需要调整大小")

                # 记录当前内容长度
                text_input._last_content_length = len(text_content)

                if should_adjust_size:
                    # 使用QTimer延迟调整大小，确保文档已完全更新
                    from PyQt6.QtCore import QTimer
                    QTimer.singleShot(50, lambda: self._adjust_text_input_size(text_input))  # 减少延迟确保及时调整
                    print(f"📏 触发大小调整: 长度={len(text_content)}, 行数={lines_count}, 变化={length_change if hasattr(text_input, '_last_content_length') else '首次'}")
                else:
                    print(f"⏭️ 跳过大小调整: 长度={len(text_content)}, 行数={lines_count}")

                print(f"文字变化: 长度={len(text_content)}, 行数={lines_count}")

        except Exception as e:
            print(f"处理文字变化失败: {e}")

    def _on_cursor_position_changed(self, text_input):
        """处理光标位置变化，确保新输入文字继承正确格式"""
        try:
            cursor = text_input.textCursor()

            # 只在没有选中文字且光标位置发生变化时才处理
            if not cursor.hasSelection():
                current_position = cursor.position()
                text_length = len(text_input.toPlainText())

                # 只在特定情况下修复格式：光标在文本末尾或刚换行后
                if current_position == text_length or self._is_after_newline(text_input, current_position):
                    char_format = cursor.charFormat()
                    color = char_format.foreground().color()

                    # 只有在颜色真的无效时才修复
                    if not color.isValid() or color.alpha() == 0:
                        self._ensure_valid_char_format(text_input, cursor)

        except Exception as e:
            # 静默处理错误，避免影响正常输入
            pass

    def _is_after_newline(self, text_input, position):
        """检查光标是否在换行符后"""
        try:
            if position > 0:
                text = text_input.toPlainText()
                if position <= len(text) and text[position - 1] == '\n':
                    return True
            return False
        except:
            return False

    def _ensure_valid_char_format(self, text_input, cursor):
        """确保字符格式有效，特别是颜色"""
        try:
            # 获取文档默认字体
            doc = text_input.document()
            default_font = doc.defaultFont()

            # 创建新的字符格式
            from PyQt6.QtGui import QTextCharFormat, QColor
            char_format = QTextCharFormat()
            char_format.setFont(default_font)

            # 设置默认颜色（从样式表获取或使用红色）
            style_sheet = text_input.styleSheet()
            default_color = QColor("red")  # 默认红色

            if 'color:' in style_sheet:
                import re
                color_match = re.search(r'color:\s*([^;]+);', style_sheet)
                if color_match:
                    color_str = color_match.group(1).strip()
                    try:
                        color = QColor(color_str)
                        if color.isValid():
                            default_color = color
                    except:
                        pass

            char_format.setForeground(default_color)

            # 应用字符格式
            cursor.setCharFormat(char_format)

        except Exception as e:
            pass

    def _fix_text_format_issues(self, text_input):
        """修复文本格式问题，特别是换行后的透明文字问题"""
        try:
            text_content = text_input.toPlainText()
            if not text_content or '\n' not in text_content:
                return

            cursor = text_input.textCursor()
            original_position = cursor.position()
            original_anchor = cursor.anchor()

            # 检查是否有选中状态，如果有则不进行修复（避免干扰选择操作）
            if cursor.hasSelection():
                return

            # 找到所有换行符的位置
            newline_positions = []
            for i, char in enumerate(text_content):
                if char == '\n':
                    newline_positions.append(i)

            # 检查每个换行符后的第一个字符
            for newline_pos in newline_positions:
                next_char_pos = newline_pos + 1
                if next_char_pos < len(text_content):
                    cursor.setPosition(next_char_pos)
                    cursor.setPosition(next_char_pos + 1, cursor.MoveMode.KeepAnchor)
                    char_format = cursor.charFormat()

                    # 检查颜色是否有效
                    color = char_format.foreground().color()
                    if not color.isValid() or color.alpha() == 0:
                        # 设置默认颜色
                        from PyQt6.QtGui import QTextCharFormat, QColor
                        fix_format = QTextCharFormat()
                        fix_format.setForeground(QColor("red"))
                        cursor.mergeCharFormat(fix_format)
                        print(f"修复位置 {next_char_pos} 的字符颜色")

            # 恢复光标位置和选择状态
            cursor.setPosition(original_anchor)
            cursor.setPosition(original_position, cursor.MoveMode.KeepAnchor if original_anchor != original_position else cursor.MoveMode.MoveAnchor)
            text_input.setTextCursor(cursor)

        except Exception as e:
            print(f"修复文本格式时出错: {e}")

    def _remove_text_input(self, text_input):
        """移除文本输入框"""
        try:
            print(f"🗑️ 开始移除文本输入框: {text_input}")

            # 从列表中移除
            if hasattr(self, 'text_inputs') and text_input in self.text_inputs:
                self.text_inputs.remove(text_input)
                print(f"✅ 从列表中移除成功，剩余输入框数量: {len(self.text_inputs)}")
            else:
                print(f"⚠️ 输入框不在列表中或列表不存在")

            # 清理最近点击的输入框记录
            if hasattr(self, '_last_clicked_input') and self._last_clicked_input == text_input:
                self._last_clicked_input = None
                print(f"🧹 清理最近点击的输入框记录")

            # 隐藏并删除输入框
            text_input.hide()
            text_input.deleteLater()
            print(f"✅ 输入框删除完成")

        except Exception as e:
            print(f"❌ 移除文本输入框失败: {e}")
            import traceback
            traceback.print_exc()

    def _adjust_text_input_size(self, text_input):
        """智能的输入框自动缩放，支持富文本和换行"""
        try:
            # 获取文本内容
            text_content = text_input.toPlainText()
            if not text_content.strip():
                # 空内容时设置最小尺寸
                current_pos = text_input.pos()
                text_input.setGeometry(current_pos.x(), current_pos.y(), 100, 40)
                return

            # 获取当前位置（固定）
            current_pos = text_input.pos()

            # 方法1：使用文档的实际大小并精确计算对称边距
            document = text_input.document()

            # 设置文档宽度为无限大，不强制换行
            document.setTextWidth(-1)  # -1 表示无限宽度，不换行

            # 获取文档的理想大小
            doc_size = document.size()
            ideal_width = int(doc_size.width())
            ideal_height = int(doc_size.height())

            # 加上边距
            padding = 15  # 内边距
            final_width = ideal_width + padding * 2
            final_height = ideal_height + padding * 2

            # 设置最小尺寸
            min_width = 80
            min_height = 35

            final_width = max(final_width, min_width)
            final_height = max(final_height, min_height)

            # 不换行模式下不需要滚动条
            text_input.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
            text_input.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

            # 设置新尺寸，位置不变
            text_input.setGeometry(current_pos.x(), current_pos.y(), final_width, final_height)

            print(f"文本框大小调整: {final_width}x{final_height}, 文档大小: {ideal_width}x{ideal_height}")

        except Exception as e:
            print(f"调整大小失败: {e}")
            # 失败时使用备用方案
            try:
                current_pos = text_input.pos()
                text_input.setGeometry(current_pos.x(), current_pos.y(), 200, 50)
            except:
                pass

    def _text_input_mouse_press(self, event, text_input):
        """文本输入框鼠标按下事件"""
        try:
            # 记录最近点击的输入框，用于切换焦点
            self._last_clicked_input = text_input
            print(f"🖱️ 记录最近点击的输入框: {text_input}")

            # 确保输入框获得焦点
            text_input.setFocus()

            if event.button() == Qt.MouseButton.RightButton:
                # 右键开始拖动
                self.dragging_text_input = text_input
                self.drag_start_pos = event.globalPosition().toPoint()
                self.text_input_start_pos = text_input.pos()
            else:
                # 左键正常处理文本选择
                QTextEdit.mousePressEvent(text_input, event)

        except Exception as e:
            print(f"文本输入框鼠标按下事件失败: {e}")

    def _text_input_mouse_move(self, event, text_input):
        """文本输入框鼠标移动事件"""
        try:
            if hasattr(self, 'dragging_text_input') and self.dragging_text_input == text_input:
                # 右键拖动
                if hasattr(self, 'drag_start_pos') and hasattr(self, 'text_input_start_pos'):
                    current_pos = event.globalPosition().toPoint()
                    delta = current_pos - self.drag_start_pos
                    new_pos = self.text_input_start_pos + delta
                    text_input.move(new_pos)
            else:
                # 左键正常处理文本选择
                QTextEdit.mouseMoveEvent(text_input, event)

        except Exception as e:
            print(f"文本输入框鼠标移动事件失败: {e}")

    def _text_input_mouse_release(self, event, text_input):
        """文本输入框鼠标释放事件"""
        try:
            if event.button() == Qt.MouseButton.RightButton:
                # 结束拖动
                if hasattr(self, 'dragging_text_input'):
                    delattr(self, 'dragging_text_input')
                if hasattr(self, 'drag_start_pos'):
                    delattr(self, 'drag_start_pos')
                if hasattr(self, 'text_input_start_pos'):
                    delattr(self, 'text_input_start_pos')
            else:
                # 左键正常处理
                QTextEdit.mouseReleaseEvent(text_input, event)

        except Exception as e:
            print(f"文本输入框鼠标释放事件失败: {e}")

    def _text_input_key_press(self, event, text_input):
        """文本输入框键盘按下事件"""
        try:
            from PyQt6.QtCore import Qt
            from PyQt6.QtWidgets import QTextEdit

            # 检查是否是可能导致跳动的按键
            if event.key() == Qt.Key.Key_Space:
                # 空格键：正常处理但防止触发大小调整
                QTextEdit.keyPressEvent(text_input, event)
                print(f"⌨️ 处理空格键，跳过大小调整")
                return

            # 先处理正常的键盘事件
            QTextEdit.keyPressEvent(text_input, event)

            # 检查是否是删除相关的按键
            if event.key() in [Qt.Key.Key_Backspace, Qt.Key.Key_Delete]:
                print(f"⌨️ 检测到删除键: {event.key()}")
                # 使用延迟检查，确保文本已经被删除
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(10, lambda: self._check_empty_after_delete(text_input))

            # 检查是否是Ctrl+A然后删除的组合
            elif event.key() in [Qt.Key.Key_A] and event.modifiers() & Qt.KeyboardModifier.ControlModifier:
                print(f"⌨️ 检测到Ctrl+A全选")
                # 延迟检查，用户可能接下来会删除
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(100, lambda: self._check_empty_after_delete(text_input))

            # 对于其他按键，检查是否需要调整大小
            elif event.key() not in [Qt.Key.Key_Shift, Qt.Key.Key_Control, Qt.Key.Key_Alt, Qt.Key.Key_Meta]:
                # 文字输入类按键，通过文本变化事件处理大小调整
                # 这里不直接调整，避免重复调整
                pass

        except Exception as e:
            print(f"❌ 文本输入框键盘事件失败: {e}")

    def _text_input_wheel_event(self, event, text_input):
        """文本输入框滚轮事件 - 禁用滚动以防止跳动"""
        try:
            # 完全忽略滚轮事件，防止文字跳动
            event.ignore()
            print(f"🚫 忽略滚轮事件，防止文字跳动")
        except Exception as e:
            print(f"❌ 滚轮事件处理失败: {e}")

    def _check_empty_after_delete(self, text_input):
        """删除操作后检查是否为空"""
        try:
            if text_input and not text_input.isHidden():
                text_content = text_input.toPlainText()
                text_content_stripped = text_content.strip()

                print(f"🔍 删除后检查: 原始长度={len(text_content)}, 去空白后长度={len(text_content_stripped)}")
                print(f"🔍 内容: '{text_content}' -> '{text_content_stripped}'")

                if not text_content_stripped:
                    print(f"🗑️ 删除后检测到空文本，删除输入框")
                    self._remove_text_input(text_input)

        except Exception as e:
            print(f"❌ 删除后检查失败: {e}")

    def _delayed_remove_if_empty(self, text_input):
        """延迟检查并删除空的输入框"""
        try:
            if text_input and not text_input.isHidden():
                text_content = text_input.toPlainText()
                text_content_stripped = text_content.strip()

                print(f"⏰ 延迟检查: 原始长度={len(text_content)}, 去空白后长度={len(text_content_stripped)}")
                print(f"⏰ 内容: '{text_content}' -> '{text_content_stripped}'")

                if not text_content_stripped:
                    print(f"🗑️ 延迟检查确认空文本，删除输入框")
                    self._remove_text_input(text_input)
                else:
                    print(f"✅ 延迟检查发现有内容，保留输入框")

        except Exception as e:
            print(f"❌ 延迟删除检查失败: {e}")

    def get_current_text_selection(self):
        """获取当前是否有选中的文字"""
        try:
            # 获取当前焦点的文本输入框
            current_input = self._get_current_text_input()
            if current_input:
                cursor = current_input.textCursor()
                return cursor.hasSelection()
            return False
        except Exception as e:
            print(f"获取文字选择状态失败: {e}")
            return False

    def _get_current_text_input(self):
        """获取当前活动的文本输入框"""
        try:
            if hasattr(self, 'text_inputs') and self.text_inputs:
                # 方法1：检查有焦点的输入框
                for text_input in self.text_inputs:
                    if text_input.hasFocus():
                        print(f"✅ 找到有焦点的文本输入框: {text_input}")
                        return text_input

                # 方法2：检查鼠标位置下的输入框
                from PyQt6.QtGui import QCursor
                from PyQt6.QtCore import QPoint
                mouse_pos = QCursor.pos()
                for text_input in self.text_inputs:
                    # 将全局坐标转换为输入框的本地坐标
                    local_pos = text_input.mapFromGlobal(mouse_pos)
                    if text_input.rect().contains(local_pos):
                        print(f"✅ 找到鼠标位置下的文本输入框: {text_input}")
                        return text_input

                # 方法3：检查最近点击的输入框（如果有记录的话）
                if hasattr(self, '_last_clicked_input') and self._last_clicked_input in self.text_inputs:
                    print(f"✅ 返回最近点击的文本输入框: {self._last_clicked_input}")
                    return self._last_clicked_input

                # 方法4：如果以上都没有，返回最后创建的输入框
                if self.text_inputs:
                    last_input = self.text_inputs[-1]
                    print(f"⚠️  返回最后创建的文本输入框: {last_input}")
                    return last_input

            print("❌ 没有找到任何文本输入框")
            return None
        except Exception as e:
            print(f"❌ 获取当前文本输入框失败: {e}")
            return None

    # 字体信号处理方法（2条）
    def _on_font_changed_unselected(self, font_name):
        """处理未选中文字的字体变化"""
        print(f"=== 接收到未选中文字字体变化信号: {font_name} ===")
        try:
            current_input = self._get_current_text_input()
            print(f"当前文本输入框: {current_input}")
            if current_input:
                print(f"文本输入框内容: '{current_input.toPlainText()}'")
                print(f"文本输入框是否有焦点: {current_input.hasFocus()}")

                # 检查字体是否可用（包括动态加载的字体）
                from PyQt6.QtGui import QFontDatabase
                available_fonts = QFontDatabase.families()

                if font_name in available_fonts:
                    print(f"✅ 字体 '{font_name}' 可用")
                else:
                    print(f"⚠️ 字体 '{font_name}' 不在可用字体列表中")
                    print(f"可用字体示例: {available_fonts[:10]}")

                # 直接使用传入的字体名称，因为它来自我们的字体下拉框
                actual_font_name = font_name

                # 保存当前光标位置和选择状态
                cursor = current_input.textCursor()
                original_position = cursor.position()
                original_anchor = cursor.anchor()

                # 使用最简单直接的方法设置字体

                # 方法1：设置控件的默认字体
                from PyQt6.QtGui import QFont
                new_font = QFont(actual_font_name)
                current_input.setFont(new_font)
                print(f"✅ 方法1完成：设置控件字体为 {actual_font_name}")

                # 方法2：设置文档默认字体
                doc = current_input.document()
                doc_font = QFont(actual_font_name)
                doc.setDefaultFont(doc_font)
                print(f"✅ 方法2完成：设置文档默认字体为 {actual_font_name}")

                # 方法3：如果有现有文字，逐字符设置字体（确保多行文字都被处理）
                text_content = current_input.toPlainText()
                if text_content:
                    print(f"✅ 方法3开始：设置现有文字字体，内容长度: {len(text_content)}")

                    # 逐字符设置字体，确保所有文字都被正确处理
                    for i in range(len(text_content)):
                        cursor.setPosition(i)
                        cursor.setPosition(i + 1, cursor.MoveMode.KeepAnchor)

                        # 获取当前字符的格式并只修改字体
                        char_format = cursor.charFormat()
                        char_format.setFontFamily(actual_font_name)
                        cursor.setCharFormat(char_format)

                    print(f"✅ 方法3完成：逐字符设置字体，处理了 {len(text_content)} 个字符")
                else:
                    print("⏭️ 方法3跳过：没有现有文字")

                # 恢复光标位置和选择状态
                cursor.setPosition(original_anchor)
                cursor.setPosition(original_position, cursor.MoveMode.KeepAnchor if original_anchor != original_position else cursor.MoveMode.MoveAnchor)
                current_input.setTextCursor(cursor)

                # 方法4：更新样式表（可选，主要用于确保一致性）
                try:
                    current_style = current_input.styleSheet()
                    import re
                    # 移除现有的 font-family 设置
                    new_style = re.sub(r'font-family:\s*[^;]+;', '', current_style)
                    # 添加新的 font-family 设置
                    if 'QTextEdit {' in new_style:
                        new_style = new_style.replace('QTextEdit {', f"QTextEdit {{ font-family: '{font_name}';")
                    else:
                        new_style = f"QTextEdit {{ font-family: '{font_name}'; }} " + new_style
                    current_input.setStyleSheet(new_style)
                    print(f"方法4完成：更新样式表")
                except Exception as e:
                    print(f"方法4失败：样式表更新错误 {e}")

                # 强制刷新显示和文档
                doc = current_input.document()
                doc.markContentsDirty(0, doc.characterCount())

                # 强制重新布局文档
                doc.documentLayout().update()

                # 多重刷新确保更新
                current_input.update()
                current_input.repaint()
                current_input.viewport().update()

                # 强制处理所有待处理的事件
                from PyQt6.QtWidgets import QApplication
                QApplication.processEvents()

                # 验证字体设置是否生效
                QTimer.singleShot(50, lambda: self._verify_font_change(current_input, font_name))

                # 字体变化后延迟重新调整大小，确保样式更新完成
                QTimer.singleShot(200, lambda: self._adjust_text_input_size(current_input))

                print(f"=== 设置默认字体成功: {font_name} ===")
            else:
                print("=== 错误：没有找到当前文本输入框 ===")
        except Exception as e:
            print(f"=== 设置默认字体失败: {e} ===")
            import traceback
            traceback.print_exc()

    def _verify_font_change(self, text_input, expected_font_name):
        """验证字体设置是否生效"""
        try:
            # 检查文档默认字体
            doc_font = text_input.document().defaultFont()
            print(f"文档默认字体: {doc_font.family()}")

            # 检查第一个字符的字体
            if text_input.toPlainText():
                cursor = text_input.textCursor()
                cursor.setPosition(0)
                cursor.setPosition(1, cursor.MoveMode.KeepAnchor)
                char_format = cursor.charFormat()
                char_font = char_format.font()
                print(f"第一个字符字体: {char_font.family()}")

                if char_font.family() != expected_font_name:
                    print(f"警告：字体设置可能未生效！期望: {expected_font_name}, 实际: {char_font.family()}")
                else:
                    print(f"字体设置验证成功: {expected_font_name}")

        except Exception as e:
            print(f"字体验证失败: {e}")

    def _on_font_changed_selected(self, font_name):
        """处理选中文字的字体变化"""
        print(f"接收到选中文字字体变化信号: {font_name}")
        try:
            current_input = self._get_current_text_input()
            if current_input:
                cursor = current_input.textCursor()
                if cursor.hasSelection():
                    # 创建一个只包含字体名称的格式对象
                    from PyQt6.QtGui import QTextCharFormat
                    font_format = QTextCharFormat()
                    font_format.setFontFamily(font_name)

                    # 使用 mergeCharFormat 只合并字体名称，保留其他属性（如颜色）
                    cursor.mergeCharFormat(font_format)

                    # 确保光标保持选中状态
                    current_input.setTextCursor(cursor)

                    # 强制刷新显示
                    current_input.update()
                    current_input.repaint()

                    # 字体变化后延迟重新调整大小，确保样式更新完成
                    from PyQt6.QtCore import QTimer
                    QTimer.singleShot(100, lambda: self._adjust_text_input_size(current_input))

                    print(f"设置选中文字字体成功: {font_name}")
                else:
                    print("没有选中文字")
            else:
                print("没有找到当前文本输入框")
        except Exception as e:
            print(f"设置选中文字字体失败: {e}")
            import traceback
            traceback.print_exc()

    # 样式信号处理方法（2条）
    def _on_font_style_changed_unselected(self, bold, italic):
        """处理未选中文字的样式变化"""
        try:
            current_input = self._get_current_text_input()
            if current_input:
                # 保存当前光标位置
                cursor = current_input.textCursor()
                original_position = cursor.position()

                # 方法1：设置文档默认字体样式（影响新输入的文字）
                doc = current_input.document()
                default_font = doc.defaultFont()
                default_font.setBold(bold)
                default_font.setItalic(italic)
                doc.setDefaultFont(default_font)

                # 方法2：逐字符设置字体样式（确保多行文字都被处理）
                text_content = current_input.toPlainText()
                if text_content:
                    print(f"✅ 设置现有文字样式，内容长度: {len(text_content)}")

                    # 逐字符设置字体样式，确保所有文字都被正确处理
                    for i in range(len(text_content)):
                        cursor.setPosition(i)
                        cursor.setPosition(i + 1, cursor.MoveMode.KeepAnchor)

                        # 获取当前字符的格式并只修改字体样式
                        char_format = cursor.charFormat()
                        char_format.setFontWeight(700 if bold else 400)
                        char_format.setFontItalic(italic)
                        cursor.setCharFormat(char_format)

                    print(f"✅ 逐字符设置字体样式完成，处理了 {len(text_content)} 个字符")

                # 恢复光标位置
                cursor.setPosition(original_position)
                current_input.setTextCursor(cursor)

                # 强制刷新显示
                current_input.update()

                print(f"设置默认样式: 粗体={bold}, 斜体={italic}")
        except Exception as e:
            print(f"设置默认样式失败: {e}")
            import traceback
            traceback.print_exc()

    def _on_font_style_changed_selected(self, bold, italic):
        """处理选中文字的样式变化"""
        try:
            current_input = self._get_current_text_input()
            if current_input:
                cursor = current_input.textCursor()
                if cursor.hasSelection():
                    # 创建一个只包含字体样式的格式对象
                    from PyQt6.QtGui import QTextCharFormat
                    style_format = QTextCharFormat()
                    style_format.setFontWeight(700 if bold else 400)
                    style_format.setFontItalic(italic)

                    # 使用 mergeCharFormat 只合并字体样式，保留其他属性（如颜色）
                    cursor.mergeCharFormat(style_format)

                    # 确保光标保持选中状态
                    current_input.setTextCursor(cursor)

                    # 强制刷新显示
                    current_input.update()

                    print(f"设置选中文字样式: 粗体={bold}, 斜体={italic}")
                else:
                    print("没有选中文字")
            else:
                print("没有找到当前文本输入框")
        except Exception as e:
            print(f"设置选中文字样式失败: {e}")
            import traceback
            traceback.print_exc()

    # 大小信号处理方法（2条）
    def _on_font_size_changed_unselected(self, size):
        """处理未选中文字的大小变化"""
        print(f"接收到未选中文字大小变化信号: {size}")
        try:
            current_input = self._get_current_text_input()
            if current_input:
                # 保存当前光标位置
                cursor = current_input.textCursor()
                original_position = cursor.position()

                # 方法1：设置文档默认字体大小（影响新输入的文字）
                doc = current_input.document()
                default_font = doc.defaultFont()
                default_font.setPointSize(size)
                doc.setDefaultFont(default_font)

                # 方法2：逐字符设置字体大小（确保多行文字都被处理）
                text_content = current_input.toPlainText()
                if text_content:
                    print(f"✅ 设置现有文字大小，内容长度: {len(text_content)}")

                    # 逐字符设置字体大小，确保所有文字都被正确处理
                    for i in range(len(text_content)):
                        cursor.setPosition(i)
                        cursor.setPosition(i + 1, cursor.MoveMode.KeepAnchor)

                        # 获取当前字符的格式并只修改字体大小
                        char_format = cursor.charFormat()
                        char_format.setFontPointSize(size)
                        cursor.setCharFormat(char_format)

                    print(f"✅ 逐字符设置字体大小完成，处理了 {len(text_content)} 个字符")

                # 恢复光标位置
                cursor.setPosition(original_position)
                current_input.setTextCursor(cursor)

                # 方法3：更新样式表（确保一致性）
                current_style = current_input.styleSheet()
                import re
                if 'font-size:' in current_style:
                    new_style = re.sub(r'font-size:\s*[^;]+;', f'font-size: {size}px;', current_style)
                else:
                    new_style = current_style.replace('QTextEdit {', f'QTextEdit {{ font-size: {size}px;')
                current_input.setStyleSheet(new_style)

                # 强制刷新显示
                current_input.update()

                # 字体大小变化后延迟重新调整输入框大小，确保样式更新完成
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(100, lambda: self._adjust_text_input_size(current_input))

                print(f"设置默认字体大小成功: {size}")
            else:
                print("没有找到当前文本输入框")
        except Exception as e:
            print(f"设置默认字体大小失败: {e}")
            import traceback
            traceback.print_exc()

    def _on_font_size_changed_selected(self, size):
        """处理选中文字的大小变化"""
        print(f"接收到选中文字大小变化信号: {size}")
        try:
            current_input = self._get_current_text_input()
            if current_input:
                cursor = current_input.textCursor()
                if cursor.hasSelection():
                    # 创建一个只包含字体大小的格式对象
                    from PyQt6.QtGui import QTextCharFormat
                    size_format = QTextCharFormat()
                    size_format.setFontPointSize(size)

                    # 使用 mergeCharFormat 只合并字体大小，保留其他属性（如颜色）
                    cursor.mergeCharFormat(size_format)

                    # 确保光标保持选中状态
                    current_input.setTextCursor(cursor)

                    # 强制刷新显示
                    current_input.update()

                    # 字体大小变化后延迟重新调整输入框大小，确保样式更新完成
                    from PyQt6.QtCore import QTimer
                    QTimer.singleShot(100, lambda: self._adjust_text_input_size(current_input))

                    print(f"设置选中文字大小成功: {size}")
                else:
                    print("没有选中文字")
            else:
                print("没有找到当前文本输入框")
        except Exception as e:
            print(f"设置选中文字大小失败: {e}")
            import traceback
            traceback.print_exc()

    # 填充颜色信号处理方法（2条）
    def _on_fill_color_changed_unselected(self, color):
        """处理未选中文字的填充颜色变化"""
        print(f"接收到未选中文字颜色变化信号: {color.name()}")
        try:
            current_input = self._get_current_text_input()
            if current_input:
                # 更新样式表中的文字颜色
                current_style = current_input.styleSheet()
                import re

                # 替换或添加color属性
                if 'color:' in current_style:
                    new_style = re.sub(r'color:\s*[^;]+;', f'color: {color.name()};', current_style)
                else:
                    # 在QTextEdit块中添加color属性
                    if 'QTextEdit {' in current_style:
                        new_style = current_style.replace('QTextEdit {', f'QTextEdit {{ color: {color.name()};')
                    else:
                        new_style = f"QTextEdit {{ color: {color.name()}; }} " + current_style

                current_input.setStyleSheet(new_style)

                # 保存当前光标位置
                cursor = current_input.textCursor()
                original_position = cursor.position()

                # 设置文档默认颜色（影响新输入的文字）
                doc = current_input.document()
                default_font = doc.defaultFont()
                # 注意：QFont 没有直接的颜色设置，颜色主要通过字符格式设置

                # 设置所有现有文字的颜色，保留其他格式（逐字符处理）
                text_content = current_input.toPlainText()
                if text_content:
                    print(f"✅ 设置现有文字颜色，内容长度: {len(text_content)}")

                    # 逐字符设置颜色，确保所有文字都被正确处理
                    for i in range(len(text_content)):
                        cursor.setPosition(i)
                        cursor.setPosition(i + 1, cursor.MoveMode.KeepAnchor)

                        # 获取当前字符的格式并只修改颜色
                        char_format = cursor.charFormat()
                        char_format.setForeground(color)
                        cursor.setCharFormat(char_format)

                    print(f"✅ 逐字符设置颜色完成，处理了 {len(text_content)} 个字符")

                # 恢复光标位置
                cursor.setPosition(original_position)
                current_input.setTextCursor(cursor)

                print(f"设置默认文字颜色成功: {color.name()}")
            else:
                print("没有找到当前文本输入框")
        except Exception as e:
            print(f"设置默认文字颜色失败: {e}")

    def _on_fill_color_changed_selected(self, color):
        """处理选中文字的填充颜色变化"""
        try:
            current_input = self._get_current_text_input()
            if current_input:
                cursor = current_input.textCursor()
                if cursor.hasSelection():
                    # 创建一个只包含颜色的格式对象
                    from PyQt6.QtGui import QTextCharFormat
                    color_format = QTextCharFormat()
                    color_format.setForeground(color)

                    # 使用 mergeCharFormat 只合并颜色，保留其他属性（如字体、大小）
                    cursor.mergeCharFormat(color_format)
                    print(f"设置选中文字颜色: {color.name()}")
        except Exception as e:
            print(f"设置选中文字颜色失败: {e}")

    # 描边信号处理方法（2条）
    def _on_stroke_changed_unselected(self, color, width):
        """处理未选中文字的描边变化"""
        print(f"=== 接收到未选中文字描边变化信号: 颜色={color.name()}, 宽度={width} ===")
        try:
            current_input = self._get_current_text_input()
            if current_input:
                # 如果宽度为0，移除描边效果
                if width == 0:
                    self._remove_stroke_unselected(current_input)
                    print(f"=== 移除默认描边效果 ===")
                    return
                # 保存当前光标位置
                cursor = current_input.textCursor()
                original_position = cursor.position()

                # 方法1：设置文档默认描边（影响新输入的文字）
                doc = current_input.document()
                default_font = doc.defaultFont()
                # 注意：QFont 本身不支持描边，主要通过字符格式设置

                # 方法2：设置所有现有文字的描边，保留其他格式（逐字符处理）
                text_content = current_input.toPlainText()
                if text_content:
                    print(f"✅ 设置现有文字描边，内容长度: {len(text_content)}")

                    # 逐字符设置描边，确保所有文字都被正确处理
                    for i in range(len(text_content)):
                        char = text_content[i]

                        # 跳过换行符，不对其应用格式
                        if char == '\n':
                            continue

                        cursor.setPosition(i)
                        cursor.setPosition(i + 1, cursor.MoveMode.KeepAnchor)

                        # 获取当前字符的格式并只修改描边
                        char_format = cursor.charFormat()

                        # 设置文字轮廓（描边）
                        from PyQt6.QtGui import QPen
                        pen = QPen(color)
                        # QPen.setWidth() 需要整数，直接使用原始宽度
                        pen.setWidth(width)
                        char_format.setTextOutline(pen)
                        cursor.setCharFormat(char_format)

                    print(f"✅ 逐字符设置描边完成，处理了 {len([c for c in text_content if c != '\n'])} 个字符")
                else:
                    print("⏭️ 跳过：没有现有文字")

                # 恢复光标位置
                cursor.setPosition(original_position)
                current_input.setTextCursor(cursor)

                # 方法3：更新样式表（作为备用，主要用于新输入的文字）
                try:
                    current_style = current_input.styleSheet()
                    # 创建简单的描边效果作为备用
                    if width > 0:
                        # 使用简单的多重阴影创建描边效果
                        shadows = []
                        for x in [-width, 0, width]:
                            for y in [-width, 0, width]:
                                if x != 0 or y != 0:  # 排除中心点
                                    shadows.append(f"{x}px {y}px 0 {color.name()}")
                        stroke_style = f"text-shadow: {', '.join(shadows)};"
                    else:
                        stroke_style = "text-shadow: none;"

                    import re
                    if 'text-shadow:' in current_style:
                        new_style = re.sub(r'text-shadow:\s*[^;]+;', stroke_style, current_style)
                    else:
                        new_style = current_style.replace('QTextEdit {', f'QTextEdit {{ {stroke_style}')
                        if 'QTextEdit {' not in new_style:
                            new_style = f"QTextEdit {{ {stroke_style} }} " + new_style
                    current_input.setStyleSheet(new_style)
                    print(f"✅ 样式表描边设置完成（备用方案）")
                except Exception as e:
                    print(f"样式表描边设置失败: {e}")

                # 强制刷新显示
                current_input.update()
                current_input.repaint()

                print(f"=== 设置默认描边成功: 颜色={color.name()}, 宽度={width} ===")
            else:
                print("=== 错误：没有找到当前文本输入框 ===")
        except Exception as e:
            print(f"=== 设置默认描边失败: {e} ===")
            import traceback
            traceback.print_exc()

    def _on_stroke_changed_selected(self, color, width):
        """处理选中文字的描边变化"""
        print(f"=== 接收到选中文字描边变化信号: 颜色={color.name()}, 宽度={width} ===")
        try:
            current_input = self._get_current_text_input()
            if current_input:
                cursor = current_input.textCursor()
                if cursor.hasSelection():
                    # 如果宽度为0，移除选中文字的描边效果
                    if width == 0:
                        self._remove_stroke_selected(current_input, cursor)
                        print(f"=== 移除选中文字描边效果 ===")
                        return
                    # 创建一个只包含描边的格式对象
                    from PyQt6.QtGui import QTextCharFormat, QPen
                    stroke_format = QTextCharFormat()

                    # 设置文字轮廓（描边）
                    pen = QPen(color)
                    # QPen.setWidth() 需要整数，直接使用原始宽度
                    pen.setWidth(width)
                    stroke_format.setTextOutline(pen)

                    # 使用 mergeCharFormat 只合并描边，保留其他属性（如颜色、字体）
                    cursor.mergeCharFormat(stroke_format)

                    # 确保光标保持选中状态
                    current_input.setTextCursor(cursor)

                    # 强制刷新显示
                    current_input.update()
                    current_input.repaint()

                    print(f"=== 设置选中文字描边成功: 颜色={color.name()}, 宽度={width} ===")
                else:
                    print("=== 没有选中文字 ===")
            else:
                print("=== 错误：没有找到当前文本输入框 ===")
        except Exception as e:
            print(f"=== 设置选中文字描边失败: {e} ===")
            import traceback
            traceback.print_exc()

    def _apply_css_stroke_to_textbox(self, current_input, color, width):
        """应用CSS描边到整个文本框"""
        try:
            current_style = current_input.styleSheet()

            if width > 0:
                # 创建只向外扩展的描边效果
                shadows = []
                import math
                steps = max(8, width * 4)  # 根据宽度调整精度

                for i in range(steps):
                    angle = 2 * math.pi * i / steps
                    x = round(math.cos(angle) * width, 1)
                    y = round(math.sin(angle) * width, 1)
                    shadows.append(f"{x}px {y}px 0 {color.name()}")

                # 添加额外的阴影层以增强效果
                if width > 1:
                    for i in range(steps):
                        angle = 2 * math.pi * i / steps
                        x = round(math.cos(angle) * (width - 0.5), 1)
                        y = round(math.sin(angle) * (width - 0.5), 1)
                        shadows.append(f"{x}px {y}px 0 {color.name()}")

                stroke_style = f"text-shadow: {', '.join(shadows)};"
            else:
                stroke_style = "text-shadow: none;"

            import re
            if 'text-shadow:' in current_style:
                new_style = re.sub(r'text-shadow:\s*[^;]+;', stroke_style, current_style)
            else:
                new_style = current_style.replace('QTextEdit {', f'QTextEdit {{ {stroke_style}')
                if 'QTextEdit {' not in new_style:
                    new_style = f"QTextEdit {{ {stroke_style} }} " + new_style

            current_input.setStyleSheet(new_style)
            current_input.update()
            current_input.repaint()

        except Exception as e:
            print(f"应用CSS描边失败: {e}")

    def _remove_stroke_unselected(self, current_input):
        """移除未选中文字的描边效果"""
        try:
            # 保存当前光标位置
            cursor = current_input.textCursor()
            original_position = cursor.position()

            # 移除所有现有文字的描边
            text_content = current_input.toPlainText()
            if text_content:
                for i in range(len(text_content)):
                    char = text_content[i]

                    # 跳过换行符
                    if char == '\n':
                        continue

                    cursor.setPosition(i)
                    cursor.setPosition(i + 1, cursor.MoveMode.KeepAnchor)

                    # 移除文字轮廓（描边）
                    char_format = cursor.charFormat()
                    from PyQt6.QtGui import QPen
                    empty_pen = QPen()
                    empty_pen.setWidth(0)
                    char_format.setTextOutline(empty_pen)
                    cursor.setCharFormat(char_format)

            # 恢复光标位置
            cursor.setPosition(original_position)
            current_input.setTextCursor(cursor)

            # 移除样式表中的描边效果
            current_style = current_input.styleSheet()
            import re
            new_style = re.sub(r'text-shadow:\s*[^;]+;', 'text-shadow: none;', current_style)
            current_input.setStyleSheet(new_style)

            # 强制刷新显示
            current_input.update()
            current_input.repaint()

            print("✅ 移除描边效果完成")

        except Exception as e:
            print(f"移除默认描边失败: {e}")

    def _remove_stroke_selected(self, current_input, cursor):
        """移除选中文字的描边效果"""
        try:
            # 创建一个移除描边的格式对象
            from PyQt6.QtGui import QTextCharFormat, QPen
            remove_stroke_format = QTextCharFormat()

            # 设置空的文字轮廓（移除描边）
            empty_pen = QPen()
            empty_pen.setWidth(0)
            remove_stroke_format.setTextOutline(empty_pen)

            # 使用 mergeCharFormat 只移除描边，保留其他属性
            cursor.mergeCharFormat(remove_stroke_format)

            # 确保光标保持选中状态
            current_input.setTextCursor(cursor)

            # 强制刷新显示
            current_input.update()
            current_input.repaint()

            print("✅ 移除选中文字描边完成")

        except Exception as e:
            print(f"移除选中文字描边失败: {e}")

    # 全局属性信号处理方法（1条）
    def _on_background_changed(self, color, opacity):
        """处理背景颜色和透明度变化"""
        try:
            current_input = self._get_current_text_input()
            if current_input:
                # 设置输入框背景颜色和透明度
                rgba_color = f"rgba({color.red()}, {color.green()}, {color.blue()}, {opacity/100.0})"
                current_style = current_input.styleSheet()

                # 替换或添加background-color属性
                import re
                if 'background-color:' in current_style:
                    new_style = re.sub(r'background-color:\s*[^;]+;', f'background-color: {rgba_color};', current_style)
                else:
                    new_style = current_style.replace('QTextEdit {', f'QTextEdit {{ background-color: {rgba_color};')
                current_input.setStyleSheet(new_style)
                print(f"设置背景: 颜色={color.name()}, 透明度={opacity}%")
        except Exception as e:
            print(f"设置背景失败: {e}")

    def _on_spacing_changed(self, letter_spacing, line_spacing):
        """处理间距变化 - 支持字间距和行距"""
        print(f"接收到间距变化信号: 字间距={letter_spacing}px, 行距={line_spacing}%")

        try:
            current_input = self._get_current_text_input()
            if not current_input:
                print("没有找到当前文本输入框")
                return

            # 保存当前光标位置
            cursor = current_input.textCursor()
            original_position = cursor.position()

            # 1. 设置字间距（通过样式表）
            current_style = current_input.styleSheet()
            import re
            new_style = re.sub(r'letter-spacing:\s*[^;]+;', '', current_style)
            letter_spacing_css = f"letter-spacing: {letter_spacing}px;"

            if 'QTextEdit {' in new_style:
                new_style = new_style.replace('QTextEdit {', f'QTextEdit {{ {letter_spacing_css}')
            else:
                new_style = f"QTextEdit {{ {letter_spacing_css} }}" + new_style

            current_input.setStyleSheet(new_style)

            # 2. 设置行距（主要使用CSS方式，更可靠）
            line_height_multiplier = line_spacing / 100.0

            # 使用CSS方式设置行距
            try:
                current_style = current_input.styleSheet()
                import re
                # 移除现有的行高设置
                new_style = re.sub(r'line-height:\s*[^;]+;', '', current_style)
                # 添加新的行高设置
                line_height_css = f"line-height: {line_height_multiplier};"
                if 'QTextEdit {' in new_style:
                    new_style = new_style.replace('QTextEdit {', f'QTextEdit {{ {line_height_css}')
                else:
                    new_style = f"QTextEdit {{ {line_height_css} }}" + new_style
                current_input.setStyleSheet(new_style)
                print(f"✅ 使用CSS方式设置行距: {line_height_multiplier}")
            except Exception as css_e:
                print(f"❌ CSS行距设置失败: {css_e}")

            # 3. 备用方法：尝试通过文档格式设置（如果CSS不生效）
            try:
                doc = current_input.document()
                block = doc.firstBlock()
                blocks_processed = 0
                while block.isValid() and blocks_processed < 10:  # 限制处理的块数量
                    cursor.setPosition(block.position())
                    block_format = cursor.blockFormat()

                    # 使用最简单的方法
                    try:
                        block_format.setLineHeight(line_height_multiplier * 100, 1)
                        cursor.setBlockFormat(block_format)
                        blocks_processed += 1
                    except:
                        break

                    block = block.next()

                if blocks_processed > 0:
                    print(f"✅ 文档格式行距设置成功，处理了 {blocks_processed} 个文本块")
            except Exception as doc_e:
                print(f"文档格式行距设置跳过: {doc_e}")

            # 恢复光标位置
            cursor.setPosition(original_position)
            current_input.setTextCursor(cursor)

            # 强制刷新文档和显示
            try:
                doc.markContentsDirty(0, doc.characterCount())
                # 获取文档布局并强制重新布局
                layout = doc.documentLayout()
                if layout:
                    # 触发重新布局
                    layout.documentSizeChanged.emit(doc.size())
                current_input.update()
                current_input.repaint()
                current_input.viewport().update()
            except Exception as refresh_e:
                print(f"刷新显示时出错: {refresh_e}")
                # 简单刷新
                current_input.update()
                current_input.repaint()

            # 延迟重新调整输入框大小，确保样式更新完成
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(150, lambda: self._adjust_text_input_size(current_input))

            print(f"✅ 字间距设置完成: {letter_spacing}px")
            print(f"✅ 行距设置完成: {line_spacing}% (倍数: {line_height_multiplier})")

        except Exception as e:
            print(f"设置间距失败: {e}")
            import traceback
            traceback.print_exc()

    def _set_letter_spacing_css(self, text_input, letter_spacing):
        """设置字间距 - CSS方法"""
        try:
            current_style = text_input.styleSheet()

            # 移除现有字间距设置
            import re
            new_style = re.sub(r'letter-spacing:\s*[^;]+;', '', current_style)

            # 添加新的字间距
            letter_spacing_css = f"letter-spacing: {letter_spacing}px;"
            if 'QTextEdit {' in new_style:
                new_style = new_style.replace('QTextEdit {', f'QTextEdit {{ {letter_spacing_css}')
            else:
                new_style = f"QTextEdit {{ {letter_spacing_css} }}" + new_style

            text_input.setStyleSheet(new_style)
            print(f"字间距CSS设置: {letter_spacing}px")

        except Exception as e:
            print(f"设置字间距失败: {e}")

    def _set_line_spacing_document(self, text_input, line_spacing):
        """设置行距 - 支持负数、缩小和放大"""
        try:
            # 计算行距倍数（支持负数）
            line_multiplier = line_spacing / 100.0

            # 获取字体信息
            font = text_input.font()
            from PyQt6.QtGui import QFontMetrics
            font_metrics = QFontMetrics(font)
            base_height = font_metrics.height()

            print(f"行距计算: 基础高度={base_height}px, 倍数={line_multiplier:.2f}, 百分比={line_spacing:.2f}%")

            # 使用QTextDocument设置
            doc = text_input.document()
            cursor = text_input.textCursor()

            # 选择全部文本
            cursor.select(cursor.SelectionType.Document)

            # 设置段落格式
            block_format = cursor.blockFormat()

            if line_multiplier >= 1.0:
                # 行距大于等于100%：使用段落间距增加
                extra_spacing = int(base_height * (line_multiplier - 1.0))
                block_format.setBottomMargin(extra_spacing)
                block_format.setTopMargin(0)
                print(f"增加行距: 段落间距={extra_spacing}px")
            elif line_multiplier > 0:
                # 行距在0-100%之间：使用负的顶部边距缩小
                reduction = int(base_height * (1.0 - line_multiplier))
                block_format.setTopMargin(-reduction)
                block_format.setBottomMargin(0)
                print(f"缩小行距: 负顶部边距={-reduction}px")
            else:
                # 负数行距：极度压缩，使用更大的负边距
                negative_spacing = int(base_height * abs(line_multiplier))
                block_format.setTopMargin(-negative_spacing)
                block_format.setBottomMargin(-negative_spacing // 2)
                print(f"负数行距: 负顶部边距={-negative_spacing}px, 负底部边距={-negative_spacing // 2}px")

            cursor.setBlockFormat(block_format)

            # 强制重新布局
            doc.adjustSize()
            text_input.update()

            print(f"行距设置完成: {line_spacing:.2f}% ({line_multiplier:.2f}x)")

        except Exception as e:
            print(f"设置行距失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_default_text_properties(self):
        """获取默认文字属性"""
        try:
            # 如果文字属性窗口已存在，使用其当前值
            if hasattr(self, 'text_properties_window') and self.text_properties_window:
                font_size = 24  # 默认值
                font_family = '微软雅黑'  # 默认值

                # 获取字体大小滑块的当前值
                if hasattr(self.text_properties_window, 'font_size_slider'):
                    font_size = self.text_properties_window.font_size_slider.value()

                # 获取字体下拉框的当前值
                if hasattr(self.text_properties_window, 'font_combo'):
                    current_font = self.text_properties_window.font_combo.currentText()
                    if current_font:  # 确保不是空字符串
                        font_family = current_font

                return {
                    'fill_color': self.text_properties_window.fill_color,
                    'font_size': font_size,
                    'font_family': font_family
                }
            else:
                # 使用默认值（与文字属性窗口的初始值一致）
                return {
                    'fill_color': QColor(255, 0, 0),  # 红色
                    'font_size': 24,  # 与属性窗口默认值一致
                    'font_family': '微软雅黑'  # 系统默认字体
                }
        except Exception as e:
            print(f"获取默认文字属性失败: {e}")
            return {
                'fill_color': QColor(255, 0, 0),
                'font_size': 24,
                'font_family': '微软雅黑'
            }

    def open_export_settings(self):
        """打开导出设置"""
        try:
            # 延迟初始化编辑器模块
            self._init_editors()

            if self.export_settings is None:
                QMessageBox.critical(self, Constants.MSG_ERROR, "无法初始化导出设置模块")
                return

            # 传递合成后的封面图像到导出设置模块
            if hasattr(self.export_settings, 'set_cover_image'):
                # 生成包含所有图层的合成图像
                composite_image = self._create_composite_cover()
                if composite_image:
                    self.export_settings.set_cover_image(composite_image)
                elif self.base_image:
                    self.export_settings.set_cover_image(self.base_image)

            # 获取主窗口位置和尺寸
            main_window = self.window()  # 获取顶级窗口
            if main_window:
                main_pos = main_window.pos()
                main_size = main_window.size()

                # 计算导出设置窗口在主窗口左侧外部的位置
                export_x = main_pos.x() - 380  # 主窗口左边外侧，留20px间距（360+20）
                export_y = main_pos.y() + 100  # 距离主窗口顶部100px

                # 确保窗口不会超出屏幕左边界
                if export_x < 0:
                    export_x = 20  # 如果超出左边界，设置为距离屏幕左边20px

                # 设置导出设置窗口位置
                self.export_settings.move(export_x, export_y)

            # 显示导出设置窗口
            self.export_settings.show()
            self.export_settings.raise_()  # 确保窗口在最前面
            self.export_settings.activateWindow()  # 激活窗口

        except Exception as e:
            print(f"打开导出设置失败: {e}")
            QMessageBox.critical(self, Constants.MSG_ERROR, f"打开导出设置失败: {str(e)}")

    def export_video(self):
        """导出视频"""
        try:
            if not self.video_path:
                QMessageBox.warning(self, Constants.MSG_WARNING, "请先加载视频文件")
                return

            # 获取用户选择的输出格式
            export_settings = self._get_export_settings()
            output_format = export_settings.get('output_format', 'MP4').upper()

            # 根据用户选择的格式设置文件对话框
            format_filters = {
                'MP4': "MP4文件 (*.mp4)",
                'AVI': "AVI文件 (*.avi)",
                'MOV': "MOV文件 (*.mov)",
                'MKV': "MKV文件 (*.mkv)",
                'WMV': "WMV文件 (*.wmv)",
                'WEBM': "WEBM文件 (*.webm)",
                'FLV': "FLV文件 (*.flv)",
                'M4V': "M4V文件 (*.m4v)",
                '3GP': "3GP文件 (*.3gp)",
                'OGV': "OGV文件 (*.ogv)",
                'MPEG': "MPEG文件 (*.mpeg)",
                'TS': "TS文件 (*.ts)"
            }

            # 构建文件过滤器，优先显示用户选择的格式
            selected_filter = format_filters.get(output_format, "MP4文件 (*.mp4)")
            all_filters = ";;".join(format_filters.values()) + ";;所有文件 (*)"

            # 选择输出文件
            output_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存视频文件",
                self.last_export_path,
                all_filters,
                selected_filter  # 默认选择用户设置的格式
            )

            # 如果用户没有添加扩展名，自动添加
            if output_path and not any(output_path.lower().endswith(ext) for ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.webm', '.flv', '.m4v', '.3gp', '.ogv', '.mpeg']):
                output_path += f".{output_format.lower()}"

            if not output_path:
                return

            self.last_export_path = os.path.dirname(output_path)
            set_path('cover_edit_export', output_path)  # 保存路径

            # 获取导出设置
            export_settings = self._get_export_settings()

            # 获取FFmpeg路径和编码器
            ffmpeg_path = self._get_ffmpeg_path()
            encoder = self._get_best_encoder(ffmpeg_path)
            print(f"🎯 主类选择的编码器: {encoder}")

            # 计算裁剪范围
            start_frames = getattr(self, 'cropped_start_frames', Constants.DEFAULT_CROP_START)
            end_frames = getattr(self, 'cropped_end_frames', Constants.DEFAULT_CROP_END)
            start_frame = start_frames
            end_frame = self.total_frames - end_frames

            # 确保fps是合理的值
            fps = getattr(self, 'fps', 30.0)
            if fps <= 0 or fps > 120:
                fps = 30.0

            # 使用基础封面图像
            cover_image = self.base_image

            # 创建导出线程
            self.export_thread = VideoExportWorker(
                video_path=self.video_path,
                output_path=output_path,
                cover_image=cover_image,
                fps=fps,
                start_frame=start_frame,
                end_frame=end_frame,
                ffmpeg_path=ffmpeg_path,
                export_settings=export_settings,
                encoder=encoder
            )

            # 连接信号
            self.export_thread.progress_updated.connect(self._update_export_progress)
            self.export_thread.export_finished.connect(self._on_export_finished)

            # 禁用导出按钮
            self.export_btn.setEnabled(False)
            self.export_btn.setText("导出中...")

            # 启动导出
            self.export_thread.start()

        except Exception as e:
            QMessageBox.critical(self, Constants.MSG_ERROR, f"启动导出失败: {str(e)}")
            self.export_btn.setEnabled(True)
            self.export_btn.setText("导出视频")

    def toggle_batch_mode(self, enabled):
        """切换批量处理模式"""
        if enabled:
            # 显示批量处理设置面板
            self.batch_settings_widget.show()
            self.batch_settings_shown = True
            self.batch_mode_checkbox.setText("▼ 启用批量处理模式")  # 显示向下箭头
            print("📋 批量处理面板已显示，可使用 Ctrl+1 切换日志显示")
        else:
            # 隐藏批量处理设置面板
            self.batch_settings_widget.hide()
            self.batch_settings_shown = False
            self.batch_mode_checkbox.setText("▲ 启用批量处理模式")  # 显示向上箭头

            # 同时隐藏日志面板
            if hasattr(self, 'batch_log_display'):
                self.batch_log_display.setVisible(False)
                self.batch_log_visible = False
                print("📋 批量处理面板已隐藏，日志面板也已隐藏")

            # 如果批量处理正在运行，停止它并取消勾选
            if getattr(self, 'batch_mode_enabled', False):
                self.batch_mode_enabled = False
                if hasattr(self, 'file_watcher') and self.file_watcher:
                    self.file_watcher.deleteLater()
                    self.file_watcher = None
                if hasattr(self, 'batch_status_label'):
                    self.batch_status_label.setText("状态: 已停止")
                if hasattr(self, 'add_batch_log'):
                    self.add_batch_log("批量处理已停止", "warning")
                # 取消勾选
                if hasattr(self, 'batch_enable_checkbox'):
                    self.batch_enable_checkbox.blockSignals(True)
                    self.batch_enable_checkbox.setChecked(False)
                    self.batch_enable_checkbox.blockSignals(False)

    def toggle_batch_processing(self, enabled):
        """切换批量处理启动/停止"""
        try:
            if enabled:
                # 检查必要的设置
                if not hasattr(self, 'watch_folder') or not self.watch_folder:
                    QMessageBox.warning(self, "警告", "请先设置监控文件夹")
                    self.batch_enable_checkbox.setChecked(False)
                    return
                if not hasattr(self, 'output_folder') or not self.output_folder:
                    QMessageBox.warning(self, "警告", "请先设置输出文件夹")
                    self.batch_enable_checkbox.setChecked(False)
                    return

                # 启动批量处理
                self.batch_mode_enabled = True
                self._setup_file_watcher()
                self.batch_status_label.setText("状态: 监控已启动")
                self.add_batch_log("🚀 批量处理已启动", "success")

                # 扫描现有文件
                QTimer.singleShot(1000, self._scan_existing_files)
            else:
                # 停止批量处理（优化版本）
                self._stop_batch_processing_optimized()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"切换批量处理失败: {str(e)}")
            self.batch_enable_checkbox.setChecked(False)

    def reset_batch_processing(self):
        """重置批量处理记录"""
        try:
            reply = QMessageBox.question(
                self,
                "确认重置",
                "是否要清空所有处理记录？\n这将允许重新处理之前已处理的文件。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 清空处理记录
                self.processed_files.clear()
                self.failed_files.clear()
                self.processing_queue.clear()
                self.retry_count.clear()

                # 重置状态
                self.current_processing = False
                self.scan_in_progress = False

                # 清空日志
                self.batch_log_display.clear()

                self.add_batch_log("🔄 处理记录已重置", "success")
                self.add_batch_log("现在可以重新处理之前的文件", "info")

                QMessageBox.information(self, "重置完成", "处理记录已清空，可以重新处理文件了。")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"重置失败: {str(e)}")

    def browse_watch_folder(self):
        """浏览监控文件夹"""
        try:
            folder = QFileDialog.getExistingDirectory(
                self,
                "选择监控文件夹",
                get_path('cover_edit_watch_folder') or self.watch_folder or ""
            )

            if folder:
                self.watch_folder = folder
                set_path('cover_edit_watch_folder', folder)  # 保存路径
                self.watch_folder_edit.setText(folder)

                # 如果批量模式已启用，重新设置监控
                if self.batch_mode_enabled:
                    self._setup_file_watcher()

        except Exception as e:
            QMessageBox.critical(self, Constants.MSG_ERROR, f"选择监控文件夹失败: {str(e)}")

    def browse_output_folder(self):
        """浏览输出文件夹"""
        try:
            folder = QFileDialog.getExistingDirectory(
                self,
                "选择输出文件夹",
                get_path('cover_edit_output_folder') or self.output_folder or ""
            )

            if folder:
                self.output_folder = folder
                set_path('cover_edit_output_folder', folder)  # 保存路径
                self.output_folder_edit.setText(folder)

        except Exception as e:
            QMessageBox.critical(self, Constants.MSG_ERROR, f"选择输出文件夹失败: {str(e)}")

    def _setup_file_watcher(self):
        """设置文件监控"""
        try:
            if self.file_watcher:
                self.file_watcher.deleteLater()
                self.file_watcher = None

            if not self.watch_folder or not os.path.exists(self.watch_folder):
                return

            self.file_watcher = QFileSystemWatcher()
            self.file_watcher.addPath(self.watch_folder)
            self.file_watcher.directoryChanged.connect(self._on_directory_changed)

            # 更新状态
            self.batch_status_label.setText("状态: 监控中...")

            print(f"开始监控文件夹: {self.watch_folder}")

        except Exception as e:
            print(f"设置文件监控失败: {e}")
            self.batch_status_label.setText("状态: 监控失败")

    def _on_directory_changed(self, path: str):
        """目录变化处理"""
        try:
            _ = path  # 忽略未使用的参数，但保留用于调试
            if self.scan_in_progress:
                return

            self.scan_in_progress = True

            # 延迟扫描，避免频繁触发
            QTimer.singleShot(2000, self._scan_new_files)

        except Exception as e:
            print(f"目录变化处理失败: {e}")
            self.scan_in_progress = False

    def _scan_new_files(self):
        """扫描新文件（优化版本）"""
        try:
            if not self.watch_folder or not os.path.exists(self.watch_folder):
                return

            potential_new_files = []

            # 扫描支持的视频文件
            all_files = []
            for filename in os.listdir(self.watch_folder):
                if any(filename.lower().endswith(ext) for ext in Constants.VIDEO_EXTENSIONS):
                    file_path = os.path.join(self.watch_folder, filename)
                    all_files.append((filename, file_path))

            print(f"扫描到 {len(all_files)} 个视频文件")

            for filename, file_path in all_files:
                print(f"检查文件: {filename}")

                # 跳过已处理的文件
                if file_path in self.processed_files:
                    print(f"  - 跳过已处理: {filename}")
                    continue

                # 跳过失败的文件（除非可以重试）
                if file_path in self.failed_files:
                    retry_count = self.retry_count.get(file_path, 0)
                    if retry_count >= self._max_retries:
                        print(f"  - 跳过失败文件（已达重试上限）: {filename}")
                        continue
                    else:
                        print(f"  - 失败文件可重试 ({retry_count}/{self._max_retries}): {filename}")

                # 跳过已在队列中的文件
                if file_path in self.processing_queue:
                    print(f"  - 跳过队列中文件: {filename}")
                    continue

                # 跳过输出文件（避免处理自己生成的文件）
                if "_processed" in filename or "_cover_" in filename:
                    print(f"  - 跳过输出文件: {filename}")
                    continue

                print(f"  - 添加到潜在文件列表: {filename}")
                potential_new_files.append(file_path)

            if potential_new_files:
                self.add_batch_log(f"🔍 发现 {len(potential_new_files)} 个潜在新文件，检查就绪状态...", "info")
                # 添加调试信息
                for file_path in potential_new_files:
                    print(f"潜在新文件: {os.path.basename(file_path)}")
                self._check_new_files_async(potential_new_files)
            else:
                # 没有新文件，直接结束扫描
                self.scan_in_progress = False

        except Exception as e:
            print(f"扫描新文件失败: {e}")
            self.add_batch_log(f"扫描文件失败: {str(e)}", "error")
            self.scan_in_progress = False

    def _check_new_files_async(self, file_list: list):
        """异步检查新文件列表"""
        self._new_pending_files = file_list.copy()
        self._new_ready_files = []
        self._new_files_checked = 0

        # 开始异步检查每个文件
        for file_path in file_list:
            if self._is_file_ready(file_path):
                # 文件初步检查通过，进行稳定性检查
                self._check_file_stability_async(file_path, self._on_new_file_check_complete)
            else:
                # 文件初步检查失败
                self._on_new_file_check_complete(file_path, False)

    def _on_new_file_check_complete(self, file_path: str, is_ready: bool):
        """新文件检查完成回调"""
        try:
            self._new_files_checked += 1

            if is_ready:
                self._new_ready_files.append(file_path)
                filename = os.path.basename(file_path)
                print(f"发现新文件: {filename}")
                self.add_batch_log(f"🆕 新文件就绪: {filename}", "info")

            # 检查是否所有文件都已检查完成
            if self._new_files_checked >= len(self._new_pending_files):
                self._finalize_new_file_scan()

        except Exception as e:
            print(f"新文件检查完成回调失败: {e}")

    def _finalize_new_file_scan(self):
        """完成新文件扫描"""
        try:
            if self._new_ready_files:
                # 添加就绪的新文件到处理队列
                for file_path in self._new_ready_files:
                    if file_path not in self.processing_queue:
                        self.processing_queue.append(file_path)

                ready_count = len(self._new_ready_files)
                self.add_batch_log(f"📋 添加 {ready_count} 个新文件到处理队列", "success")

                # 开始处理（如果当前没有在处理）
                if not self.current_processing:
                    QTimer.singleShot(500, self._process_next_file)

            # 清理临时变量
            self._new_pending_files = []
            self._new_ready_files = []
            self._new_files_checked = 0

            # 扫描完成
            self.scan_in_progress = False

        except Exception as e:
            print(f"完成新文件扫描失败: {e}")
            self.add_batch_log(f"完成新文件扫描失败: {str(e)}", "error")
            self.scan_in_progress = False

    def _scan_existing_files(self):
        """扫描现有文件（启动时调用）- 优化版本"""
        try:
            if not self.watch_folder or not os.path.exists(self.watch_folder):
                self.add_batch_log("❌ 监控文件夹不存在或未设置", "error")
                return

            self.add_batch_log("🔍 扫描现有文件...", "info")

            # 获取所有文件
            all_files = []
            try:
                for filename in os.listdir(self.watch_folder):
                    file_path = os.path.join(self.watch_folder, filename)
                    if os.path.isfile(file_path):  # 确保是文件而不是目录
                        all_files.append((filename, file_path))
                        print(f"发现文件: {filename} (大小: {os.path.getsize(file_path)/1024/1024:.1f}MB)")
            except Exception as e:
                self.add_batch_log(f"❌ 读取文件夹失败: {str(e)}", "error")
                return

            print(f"文件夹中共有 {len(all_files)} 个文件")

            # 筛选视频文件
            video_files = []
            for filename, file_path in all_files:
                print(f"检查文件扩展名: {filename}")
                is_video = any(filename.lower().endswith(ext) for ext in Constants.VIDEO_EXTENSIONS)
                print(f"  - 是否为视频: {is_video}")
                print(f"  - 支持的扩展名: {Constants.VIDEO_EXTENSIONS}")
                print(f"  - 文件扩展名: {os.path.splitext(filename)[1].lower()}")

                if is_video:
                    video_files.append((filename, file_path))
                    print(f"✅ 确认为视频文件: {filename}")
                else:
                    print(f"❌ 跳过非视频文件: {filename}")

            print(f"共发现 {len(video_files)} 个视频文件")
            self.add_batch_log(f"📹 发现 {len(video_files)} 个视频文件", "info")

            # 过滤文件
            potential_files = []
            for filename, file_path in video_files:
                print(f"检查文件: {filename}")

                # 跳过已处理的文件
                if file_path in self.processed_files:
                    print(f"  - 跳过已处理: {filename}")
                    self.add_batch_log(f"⏭️ 跳过已处理: {filename}", "info")
                    continue

                # 跳过失败的文件（但允许重试）
                if file_path in self.failed_files:
                    retry_count = self.retry_count.get(file_path, 0)
                    if retry_count >= self._max_retries:
                        print(f"  - 跳过失败文件（已达重试上限）: {filename}")
                        self.add_batch_log(f"❌ 跳过失败文件（重试{retry_count}次）: {filename}", "warning")
                        continue
                    else:
                        print(f"  - 失败文件可重试 ({retry_count}/{self._max_retries}): {filename}")
                        self.add_batch_log(f"🔄 失败文件可重试 ({retry_count}/{self._max_retries}): {filename}", "warning")

                # 跳过输出文件
                if "_processed" in filename or "_cover_" in filename:
                    print(f"  - 跳过输出文件: {filename}")
                    self.add_batch_log(f"📤 跳过输出文件: {filename}", "info")
                    continue

                # 跳过队列中的文件
                if file_path in self.processing_queue:
                    print(f"  - 跳过队列中文件: {filename}")
                    self.add_batch_log(f"📋 跳过队列中文件: {filename}", "info")
                    continue

                print(f"  - ✅ 添加到潜在文件列表: {filename}")
                potential_files.append(file_path)

            if potential_files:
                self.add_batch_log(f"📁 发现 {len(potential_files)} 个待处理文件，检查就绪状态...", "info")
                self._check_files_async(potential_files)
            else:
                self.add_batch_log("📂 未发现待处理文件", "info")

        except Exception as e:
            print(f"扫描现有文件失败: {e}")
            self.add_batch_log(f"扫描现有文件失败: {str(e)}", "error")

    def _check_files_async(self, file_list: list):
        """异步检查文件列表"""
        self._pending_files = file_list.copy()
        self._ready_files = []
        self._files_checked = 0

        # 开始异步检查每个文件
        for file_path in file_list:
            if self._is_file_ready(file_path):
                # 文件初步检查通过，进行稳定性检查
                self._check_file_stability_async(file_path, self._on_file_check_complete)
            else:
                # 文件初步检查失败
                self._on_file_check_complete(file_path, False)

    def _on_file_check_complete(self, file_path: str, is_ready: bool):
        """文件检查完成回调"""
        try:
            self._files_checked += 1

            filename = os.path.basename(file_path)
            print(f"文件检查完成: {filename} -> {'就绪' if is_ready else '未就绪'}")

            if is_ready:
                self._ready_files.append(file_path)
                self.add_batch_log(f"✅ 文件就绪: {filename}", "info")
                print(f"  - 添加到就绪列表: {filename}")
            else:
                self.add_batch_log(f"⏳ 文件未就绪: {filename}", "warning")
                print(f"  - 文件未就绪，跳过: {filename}")

            print(f"检查进度: {self._files_checked}/{len(self._pending_files)}")

            # 检查是否所有文件都已检查完成
            if self._files_checked >= len(self._pending_files):
                print("所有文件检查完成，开始最终处理")
                self._finalize_file_scan()

        except Exception as e:
            print(f"文件检查完成回调失败: {e}")

    def _finalize_file_scan(self):
        """完成文件扫描"""
        try:
            if self._ready_files:
                # 添加就绪的文件到处理队列
                for file_path in self._ready_files:
                    if file_path not in self.processing_queue:
                        self.processing_queue.append(file_path)

                ready_count = len(self._ready_files)
                total_count = len(self._pending_files)
                self.add_batch_log(f"📋 添加 {ready_count}/{total_count} 个文件到处理队列", "success")

                # 开始处理
                if not self.current_processing:
                    QTimer.singleShot(1000, self._process_next_file)
            else:
                self.add_batch_log("📂 没有就绪的文件可处理", "info")

            # 清理临时变量
            self._pending_files = []
            self._ready_files = []
            self._files_checked = 0

        except Exception as e:
            print(f"完成文件扫描失败: {e}")
            self.add_batch_log(f"完成文件扫描失败: {str(e)}", "error")

    def _is_file_ready(self, file_path: str) -> bool:
        """检查文件是否准备就绪（同步版本，用于初步检查）"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"文件不存在: {os.path.basename(file_path)}")
                return False

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                print(f"文件大小为0: {os.path.basename(file_path)}")
                return False

            # 检查文件是否被占用（尝试以独占模式打开）
            try:
                with open(file_path, 'rb') as f:
                    # 尝试读取一小部分数据
                    f.read(1024)
            except (IOError, OSError):
                # 文件被占用或无法读取
                print(f"文件被占用或无法读取: {os.path.basename(file_path)}")
                return False

            # 检查视频时长（新增）- 但不影响就绪状态
            duration = self._get_video_duration_quick(file_path)
            if duration is not None:
                print(f"文件 {os.path.basename(file_path)} 时长: {duration:.1f}秒")
                if duration <= 0:
                    print(f"警告: 获取到无效时长，但仍允许处理")
                elif duration > 180:
                    print(f"注意: 长视频文件 ({duration:.1f}秒 = {duration/60:.1f}分钟)")
            else:
                print(f"警告: 无法获取 {os.path.basename(file_path)} 的时长信息，但仍允许处理")

            # 文件大小大于100KB即认为可能就绪（时长检查不影响就绪状态）
            is_ready = file_size > 100 * 1024
            print(f"文件就绪检查结果: {os.path.basename(file_path)} -> {is_ready} (大小: {file_size/1024/1024:.1f}MB)")
            return is_ready

        except Exception as e:
            print(f"检查文件就绪状态失败: {e}")
            return False

    def _get_video_duration_quick(self, file_path: str) -> Optional[float]:
        """快速获取视频时长（用于调试）"""
        try:
            import cv2
            print(f"  - 尝试打开视频: {os.path.basename(file_path)}")

            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                print(f"  - 默认后端无法打开，尝试FFmpeg后端...")
                cap = cv2.VideoCapture(file_path, cv2.CAP_FFMPEG)
                if not cap.isOpened():
                    print(f"  - 所有后端都无法打开视频: {os.path.basename(file_path)}")
                    return None

            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            fps = cap.get(cv2.CAP_PROP_FPS)

            print(f"  - 视频参数: 帧数={frame_count}, 帧率={fps}")

            cap.release()

            if fps > 0 and frame_count > 0:
                duration = frame_count / fps
                print(f"  - 计算时长: {duration:.1f}秒")
                return duration
            else:
                print(f"  - 无效的视频参数: 帧数={frame_count}, 帧率={fps}")
                return None

        except Exception as e:
            print(f"  - 快速获取时长异常: {e}")
            return None

    def _check_file_stability_async(self, file_path: str, callback):
        """异步检查文件稳定性"""
        try:
            filename = os.path.basename(file_path)
            print(f"开始异步稳定性检查: {filename}")

            if not os.path.exists(file_path):
                print(f"文件不存在，标记为未就绪: {filename}")
                callback(file_path, False)
                return

            initial_size = os.path.getsize(file_path)
            print(f"初始文件大小: {filename} -> {initial_size/1024/1024:.1f}MB")

            # 使用定时器延迟检查，避免阻塞UI
            def check_stability():
                try:
                    if not os.path.exists(file_path):
                        print(f"稳定性检查时文件不存在: {filename}")
                        callback(file_path, False)
                        return

                    current_size = os.path.getsize(file_path)
                    is_stable = current_size == initial_size and current_size > 100 * 1024

                    print(f"稳定性检查结果: {filename}")
                    print(f"  - 初始大小: {initial_size/1024/1024:.1f}MB")
                    print(f"  - 当前大小: {current_size/1024/1024:.1f}MB")
                    print(f"  - 大小稳定: {current_size == initial_size}")
                    print(f"  - 大小足够: {current_size > 100 * 1024}")
                    print(f"  - 最终结果: {'稳定' if is_stable else '不稳定'}")

                    if not is_stable:
                        print(f"文件未稳定: {filename}, 大小: {initial_size} -> {current_size}")

                    # 重要：即使文件大小检查失败，对于大文件也应该允许处理
                    # 特别是对于长视频，可能存在元数据更新导致的微小变化
                    if not is_stable and current_size > 10 * 1024 * 1024:  # 大于10MB的文件
                        print(f"大文件特殊处理: {filename} ({current_size/1024/1024:.1f}MB) - 允许处理")
                        is_stable = True

                    callback(file_path, is_stable)

                except Exception as e:
                    print(f"异步检查文件稳定性失败: {e}")
                    # 异常情况下，对于大文件仍然允许处理
                    try:
                        file_size = os.path.getsize(file_path)
                        if file_size > 10 * 1024 * 1024:  # 大于10MB
                            print(f"异常情况下允许大文件处理: {filename}")
                            callback(file_path, True)
                        else:
                            callback(file_path, False)
                    except:
                        callback(file_path, False)

            # 1秒后检查文件稳定性
            QTimer.singleShot(1000, check_stability)

        except Exception as e:
            print(f"启动异步文件检查失败: {e}")
            callback(file_path, False)

    def _process_next_file(self):
        """处理下一个文件（优化版本）"""
        try:
            # 使用锁防止并发处理
            if self._processing_lock or not self.processing_queue or self.current_processing:
                return

            self._processing_lock = True
            file_path = self.processing_queue.pop(0)
            self.current_processing = True
            self._processing_lock = False

            # 更新状态
            filename = os.path.basename(file_path)
            self.batch_status_label.setText(f"状态: 处理中 - {filename}")
            self.add_batch_log(f"🎬 开始处理: {filename}", "processing")

            # 检查重试次数
            retry_count = self.retry_count.get(file_path, 0)
            if retry_count > 0:
                self.add_batch_log(f"🔄 重试处理 ({retry_count}/{self._max_retries}): {filename}", "warning")

            # 加载视频并自动处理
            self._auto_process_video(file_path)

        except Exception as e:
            print(f"处理文件失败: {e}")
            self._processing_lock = False
            self.current_processing = False
            self._handle_processing_error(file_path if 'file_path' in locals() else None, str(e))

    def _handle_processing_error(self, file_path: str, error_msg: str):
        """处理处理错误（优化版本）"""
        try:
            if not file_path:
                # 如果没有文件路径，直接继续下一个
                QTimer.singleShot(1000, self._process_next_file)
                return

            filename = os.path.basename(file_path)
            retry_count = self.retry_count.get(file_path, 0)

            # 判断是否应该重试
            if retry_count < self._max_retries and self._should_retry_error(error_msg):
                # 增加重试次数
                self.retry_count[file_path] = retry_count + 1

                # 重新加入队列（加到前面，优先处理）
                self.processing_queue.insert(0, file_path)

                # 使用指数退避延迟重试
                delay = self._retry_delays[min(retry_count, len(self._retry_delays) - 1)]
                self.add_batch_log(f"⏰ {delay/1000}秒后重试: {filename}", "warning")

                QTimer.singleShot(delay, self._process_next_file)
            else:
                # 达到重试上限或不可重试的错误
                self.failed_files.add(file_path)
                self.add_batch_log(f"❌ 处理失败: {filename} - {error_msg}", "error")

                # 继续处理下一个文件
                QTimer.singleShot(1000, self._process_next_file)

        except Exception as e:
            print(f"处理错误处理失败: {e}")
            QTimer.singleShot(2000, self._process_next_file)

    def _should_retry_error(self, error_msg: str) -> bool:
        """判断错误是否应该重试"""
        # 可重试的错误类型
        retryable_errors = [
            "文件被占用", "权限被拒绝", "网络", "超时", "临时",
            "busy", "locked", "permission", "timeout", "temporary"
        ]

        error_lower = error_msg.lower()
        return any(keyword in error_lower for keyword in retryable_errors)

    def _auto_process_video(self, file_path: str):
        """自动处理视频（优化版本）"""
        try:
            # 释放之前的视频资源
            self._release_video_resources()

            # 加载视频
            self.load_video(file_path)

            # 等待视频加载完成
            QTimer.singleShot(1000, lambda: self._continue_auto_process(file_path))

        except FileNotFoundError:
            error_msg = f"文件不存在: {os.path.basename(file_path)}"
            print(error_msg)
            self.add_batch_log(f"❌ {error_msg}", "error")
            self.current_processing = False
            self._handle_processing_error(file_path, error_msg)
        except PermissionError:
            error_msg = f"权限被拒绝: {os.path.basename(file_path)}"
            print(error_msg)
            self.add_batch_log(f"❌ {error_msg}", "error")
            self.current_processing = False
            self._handle_processing_error(file_path, error_msg)
        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            print(f"自动处理视频失败: {e}")
            self.add_batch_log(f"❌ {os.path.basename(file_path)} - {error_msg}", "error")
            self.current_processing = False
            self._handle_processing_error(file_path, error_msg)

    def _release_video_resources(self):
        """释放视频资源"""
        try:
            if hasattr(self, 'video_cap') and self.video_cap:
                self.video_cap.release()
                self.video_cap = None

            if hasattr(self, 'media_player') and self.media_player:
                self.media_player.stop()

            # 强制垃圾回收
            import gc
            gc.collect()

        except Exception as e:
            print(f"释放视频资源失败: {e}")

    def _continue_auto_process(self, file_path: str):
        """继续自动处理视频"""
        try:
            # 检查视频是否加载成功
            if not hasattr(self, 'video_cap') or not self.video_cap or not self.video_cap.isOpened():
                self.add_batch_log(f"❌ 视频加载失败: {os.path.basename(file_path)}", "error")
                self.failed_files.add(file_path)
                self.current_processing = False
                QTimer.singleShot(1000, self._process_next_file)
                return

            # 检查视频基本信息
            if not hasattr(self, 'video_width') or not hasattr(self, 'video_height'):
                self.add_batch_log(f"❌ 无法获取视频信息: {os.path.basename(file_path)}", "error")
                self.failed_files.add(file_path)
                self.current_processing = False
                QTimer.singleShot(1000, self._process_next_file)
                return

            # 自动匹配封面（这是关键步骤）
            self.add_batch_log(f"🔍 正在匹配封面... ({self.video_width}x{self.video_height})", "processing")
            self.load_matching_cover_by_aspect_ratio()

            # 等待封面加载完成后开始导出
            QTimer.singleShot(1000, lambda: self._check_cover_and_export(file_path))

        except Exception as e:
            print(f"继续处理视频失败: {e}")
            self.add_batch_log(f"❌ 处理异常: {os.path.basename(file_path)} - {str(e)}", "error")
            self.failed_files.add(file_path)
            self.current_processing = False
            QTimer.singleShot(1000, self._process_next_file)

    def _check_cover_and_export(self, file_path: str, retry_count: int = 0, smart_capture_attempted: bool = False):
        """检查封面并开始导出（优化版本）"""
        try:
            # 检查是否有封面
            if not hasattr(self, 'base_image') or not self.base_image:
                if retry_count < 3 and not smart_capture_attempted:
                    self.add_batch_log(f"⏳ 等待封面加载... ({retry_count + 1}/3)", "processing")
                    QTimer.singleShot(1000, lambda: self._check_cover_and_export(file_path, retry_count + 1, False))
                    return
                elif not smart_capture_attempted:
                    self.add_batch_log(f"⚠️ 封面加载超时，尝试智能截取: {os.path.basename(file_path)}", "warning")
                    # 尝试智能截取作为备选方案
                    try:
                        self.smart_capture_frame()
                        QTimer.singleShot(500, lambda: self._check_cover_and_export(file_path, 0, True))
                        return
                    except Exception as e:
                        self.add_batch_log(f"❌ 智能截取失败: {str(e)}", "error")
                        self._handle_cover_failure(file_path, "智能截取失败")
                        return
                else:
                    # 智能截取也失败了
                    self._handle_cover_failure(file_path, "无法获取封面")
                    return

            # 生成唯一输出路径
            output_path = self._generate_unique_output_path(file_path)
            if not output_path:
                self._handle_cover_failure(file_path, "无法生成输出路径")
                return

            self.add_batch_log(f"🚀 开始导出: {os.path.basename(output_path)}", "processing")

            # 显示进度条（批量处理时）
            if hasattr(self, 'export_progress'):
                self.export_progress.setVisible(True)
                self.export_progress.setValue(0)

            # 开始导出
            self._start_batch_export(output_path, file_path)

        except Exception as e:
            print(f"检查封面并导出失败: {e}")
            self.add_batch_log(f"❌ 导出准备失败: {os.path.basename(file_path)} - {str(e)}", "error")
            self.current_processing = False
            self._handle_processing_error(file_path, str(e))

    def _handle_cover_failure(self, file_path: str, reason: str):
        """处理封面获取失败"""
        self.add_batch_log(f"❌ {reason}，跳过处理: {os.path.basename(file_path)}", "error")
        self.current_processing = False
        self._handle_processing_error(file_path, reason)

    def _generate_unique_output_path(self, file_path: str) -> str:
        """生成唯一的输出路径"""
        try:
            if not self.output_folder:
                self.output_folder = self.watch_folder

            filename = os.path.basename(file_path)
            name, _ = os.path.splitext(filename)

            # 清理文件名中的特殊字符，避免FFmpeg参数错误
            safe_name = self._sanitize_filename(name)

            # 使用UUID确保唯一性
            import uuid
            unique_id = str(uuid.uuid4())[:8]

            # 使用时间戳和UUID
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 获取导出格式
            export_settings = self._get_export_settings()
            output_format = export_settings.get('output_format', 'MP4').lower()

            output_path = os.path.join(self.output_folder, f"{safe_name}_cover_{timestamp}_{unique_id}.{output_format}")

            return output_path

        except Exception as e:
            print(f"生成输出路径失败: {e}")
            return None



    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名中的特殊字符"""
        import re
        # 移除或替换可能导致FFmpeg问题的字符
        # 保留中文字符，但移除特殊符号
        safe_name = re.sub(r'[<>:"/\\|?*@#]', '_', filename)
        safe_name = re.sub(r'[()（）]', '', safe_name)  # 移除括号
        safe_name = re.sub(r'\s+', '_', safe_name)  # 空格替换为下划线
        safe_name = safe_name.strip('_')  # 移除首尾下划线

        # 如果文件名太长，截断
        if len(safe_name) > 50:
            safe_name = safe_name[:50]

        # 如果清理后为空，使用默认名称
        if not safe_name:
            safe_name = "video"

        return safe_name

    def _start_batch_export(self, output_path: str, original_path: str):
        """开始批量导出"""
        try:
            # 获取导出设置
            export_settings = self._get_export_settings()
            ffmpeg_path = self._get_ffmpeg_path()
            encoder = self._get_best_encoder(ffmpeg_path)

            # 计算参数
            start_frames = getattr(self, 'cropped_start_frames', Constants.DEFAULT_CROP_START)
            end_frames = getattr(self, 'cropped_end_frames', Constants.DEFAULT_CROP_END)
            start_frame = start_frames
            end_frame = self.total_frames - end_frames
            fps = getattr(self, 'fps', 30.0)

            # 创建包含所有图层的合成封面
            composite_cover = self._create_composite_cover()

            # 创建导出线程
            self.export_thread = VideoExportWorker(
                video_path=self.video_path,
                output_path=output_path,
                cover_image=composite_cover if composite_cover else self.base_image,
                fps=fps,
                start_frame=start_frame,
                end_frame=end_frame,
                ffmpeg_path=ffmpeg_path,
                export_settings=export_settings,
                encoder=encoder
            )

            # 连接信号
            self.export_thread.export_finished.connect(
                lambda output_path, success, error_msg: self._on_batch_export_finished(
                    output_path, success, error_msg, original_path
                )
            )
            # 连接进度更新信号（批量处理也使用真实进度条）
            self.export_thread.progress_updated.connect(self._update_export_progress)

            # 启动导出
            self.export_thread.start()

        except Exception as e:
            print(f"启动批量导出失败: {e}")
            self.add_batch_log(f"启动导出失败: {str(e)}", "error")
            self.failed_files.add(original_path)
            self.current_processing = False
            self._process_next_file()

    def _on_batch_export_finished(self, output_path: str, success: bool, error_msg: str, original_path: str):
        """批量导出完成处理（优化版本）"""
        try:
            # 释放当前视频资源
            self._release_video_resources()

            if success:
                self.processed_files.add(original_path)
                # 清除重试计数
                if original_path in self.retry_count:
                    del self.retry_count[original_path]

                print(f"批量处理成功: {os.path.basename(output_path)}")
                self.add_batch_log(f"✅ 处理成功: {os.path.basename(output_path)}", "success")

                # 隐藏进度条（批量处理完成）
                if hasattr(self, 'export_progress'):
                    self.export_progress.setVisible(False)
                    self.export_progress.setValue(0)

                # 记录处理统计
                total_processed = len(self.processed_files)
                total_failed = len(self.failed_files)
                self.add_batch_log(f"📊 统计: 成功 {total_processed} 个，失败 {total_failed} 个", "info")
            else:
                # 处理失败，使用统一的错误处理
                print(f"批量处理失败: {error_msg}")
                self.add_batch_log(f"❌ 导出失败: {os.path.basename(original_path)} - {error_msg}", "error")

                # 隐藏进度条（批量处理失败）
                if hasattr(self, 'export_progress'):
                    self.export_progress.setVisible(False)
                    self.export_progress.setValue(0)

                # 重置处理状态，让错误处理机制接管
                self.current_processing = False
                self._handle_processing_error(original_path, error_msg)
                return

            # 成功处理完成，继续下一个文件
            self.current_processing = False
            self._schedule_next_processing()

        except Exception as e:
            print(f"批量导出完成处理失败: {e}")
            self.add_batch_log(f"❌ 处理完成回调失败: {str(e)}", "error")
            self.current_processing = False
            self._schedule_next_processing()

    def _schedule_next_processing(self):
        """安排下一个文件的处理"""
        try:
            # 检查内存使用情况
            self._check_memory_usage()

            if self.processing_queue:
                queue_size = len(self.processing_queue)
                self.add_batch_log(f"📋 队列剩余 {queue_size} 个文件", "info")

                # 根据队列大小调整延迟
                delay = 1000 if queue_size > 10 else 2000
                QTimer.singleShot(delay, self._process_next_file)
            else:
                self.batch_status_label.setText("状态: 监控中...")
                self.add_batch_log("✨ 队列处理完成，继续监控新文件", "success")

                # 队列为空时，执行一次内存清理
                self._cleanup_resources()

        except Exception as e:
            print(f"安排下一个处理失败: {e}")
            QTimer.singleShot(3000, self._process_next_file)

    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            import time
            current_time = time.time()

            # 每30秒检查一次内存
            if current_time - self._last_memory_check > 30:
                self._last_memory_check = current_time

                # 尝试使用psutil进行详细监控
                memory_info = self._get_memory_info()

                if memory_info:
                    memory_mb = memory_info
                    if memory_mb > 1500:  # 超过1.5GB时清理
                        self._cleanup_resources()
                        self.add_batch_log(f"🧹 内存清理: {memory_mb:.1f}MB", "info")
                else:
                    # 没有psutil，执行基础清理
                    self._cleanup_resources()

        except Exception as e:
            print(f"内存检查失败: {e}")

    def _get_memory_info(self):
        """获取内存信息（兼容psutil缺失的情况）"""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            return memory_mb
        except ImportError:
            # psutil未安装，返回None
            return None
        except Exception as e:
            print(f"获取内存信息失败: {e}")
            return None

    def _cleanup_resources(self):
        """清理资源"""
        try:
            # 强制垃圾回收
            import gc
            gc.collect()

            # 清理临时文件
            for temp_file in self.temp_converted_files[:]:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        self.temp_converted_files.remove(temp_file)
                except Exception as e:
                    print(f"清理临时文件失败: {e}")

        except Exception as e:
            print(f"资源清理失败: {e}")

    def _check_video_has_audio(self, video_path: str) -> bool:
        """检查视频是否有音频轨道"""
        try:
            import subprocess
            # 使用完整的ffprobe路径
            ffprobe_path = os.path.join(os.path.dirname(self.ffmpeg_path), 'ffprobe.exe')
            if not os.path.exists(ffprobe_path):
                ffprobe_path = 'ffprobe'  # 回退到系统路径

            cmd = [
                ffprobe_path, '-v', 'error',
                '-select_streams', 'a:0',
                '-show_entries', 'stream=codec_type',
                '-of', 'csv=p=0',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            has_audio = result.returncode == 0 and 'audio' in result.stdout
            print(f"音频检查命令: {' '.join(cmd)}")
            print(f"检查结果: {result.stdout.strip()}")
            return has_audio

        except Exception as e:
            print(f"音频检查失败: {e}")
            return True  # 默认假设有音频

    def _stop_batch_processing_optimized(self):
        """优化的停止批量处理方法"""
        try:
            # 停止批量处理
            self.batch_mode_enabled = False

            # 停止文件监控
            if hasattr(self, 'file_watcher') and self.file_watcher:
                self.file_watcher.deleteLater()
                self.file_watcher = None

            # 停止内存监控定时器
            if hasattr(self, '_memory_check_timer') and self._memory_check_timer:
                self._memory_check_timer.stop()
                self._memory_check_timer = None

            # 清理文件检查定时器
            if hasattr(self, '_file_check_timers'):
                for timer in self._file_check_timers.values():
                    if timer and timer.isActive():
                        timer.stop()
                self._file_check_timers.clear()

            # 等待当前处理完成
            if self.current_processing:
                self.add_batch_log("⏳ 等待当前文件处理完成...", "warning")

            # 清理资源
            self._cleanup_resources()

            # 更新UI状态
            self.batch_status_label.setText("状态: 已停止")
            self.add_batch_log("🛑 批量处理已停止", "warning")

        except Exception as e:
            print(f"停止批量监控失败: {e}")
            self.add_batch_log(f"停止监控失败: {str(e)}", "error")

    # =============================================================
    # 辅助方法
    # =============================================================

    def add_cover_layer(self, pixmap: QPixmap, layer_type: str = "imported", source_info: str = ""):
        """添加封面图层"""
        try:
            if not pixmap or pixmap.isNull():
                return

            # 创建图层信息
            layer_info = {
                'id': self.layer_counter,
                'pixmap': pixmap,
                'type': layer_type,
                'source': source_info,
                'visible': True,
                'opacity': 1.0
            }

            # 添加到图层列表
            self.cover_layers.append(layer_info)
            self.layer_counter += 1

            # 更新合成封面
            self.update_composite_cover()

        except Exception as e:
            print(f"添加图层失败: {e}")

    def update_composite_cover(self):
        """更新合成封面"""
        try:
            if not self.cover_layers:
                # 没有图层时显示默认状态
                self.base_image = None
                self.cover_preview.clear()
                self.cover_preview.setText("封面预览")

                # 禁用文字编辑按钮
                if hasattr(self, 'text_edit_btn_export'):
                    self.text_edit_btn_export.setEnabled(False)
                return

            # 创建合成图像
            composite_pixmap = self.create_composite_image()

            if composite_pixmap and not composite_pixmap.isNull():
                # 保存原始封面（用于实时合成）
                self.original_cover = composite_pixmap.copy()

                self.base_image = composite_pixmap
                self.cover_preview.set_pixmap(self.base_image)

                # 启用文字编辑按钮
                if hasattr(self, 'text_edit_btn_export'):
                    self.text_edit_btn_export.setEnabled(True)

                # 更新其他模块的封面图像
                if hasattr(self.export_settings, 'set_cover_image'):
                    self.export_settings.set_cover_image(self.base_image)
            else:
                # 如果合成失败，也要清除显示
                self.base_image = None
                self.cover_preview.clear()
                self.cover_preview.setText("封面预览")

                # 禁用文字编辑按钮
                if hasattr(self, 'text_edit_btn_export'):
                    self.text_edit_btn_export.setEnabled(False)



        except Exception as e:
            print(f"更新合成封面失败: {e}")
            import traceback
            traceback.print_exc()

    def create_composite_image(self) -> Optional[QPixmap]:
        """创建合成图像"""
        try:
            if not self.cover_layers:
                return None

            # 使用第一个图层作为基础
            base_layer = self.cover_layers[0]
            base_pixmap = base_layer['pixmap']

            if len(self.cover_layers) == 1:
                return base_pixmap

            # 创建合成图像
            composite = QPixmap(base_pixmap.size())
            composite.fill(Qt.GlobalColor.transparent)

            painter = QPainter(composite)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 绘制所有可见图层
            for layer in self.cover_layers:
                if layer['visible']:
                    painter.setOpacity(layer['opacity'])
                    painter.drawPixmap(0, 0, layer['pixmap'])

            painter.end()
            return composite

        except Exception as e:
            print(f"创建合成图像失败: {e}")
            return None

    def refresh_preview_display(self):
        """刷新预览显示"""
        # TODO: 实现预览刷新
        pass

    def update_playback_position(self, position):
        """更新播放位置"""
        try:
            if hasattr(self, 'playback_slider') and self.media_player.duration() > 0:
                # 检查是否超出裁剪结束位置
                end_frames = getattr(self, 'cropped_end_frames', 0)
                end_time_ms = int(((self.total_frames - end_frames) / self.fps) * 1000)

                if position >= end_time_ms:
                    # 到达裁剪结束位置，暂停播放
                    self.media_player.pause()
                    if hasattr(self, 'play_pause_btn'):
                        self.play_pause_btn.setText("播放")
                    # 设置位置到结束位置
                    self.media_player.setPosition(end_time_ms)
                    self.playback_slider.setValue(end_time_ms)
                else:
                    # 更新滑块位置
                    self.playback_slider.setValue(position)

                # 更新时间标签（显示裁剪后的时间）
                if hasattr(self, 'time_label'):
                    start_frames = getattr(self, 'cropped_start_frames', 0)
                    start_time_ms = int((start_frames / self.fps) * 1000)

                    # 计算相对于裁剪开始的时间
                    relative_position = max(0, position - start_time_ms)
                    current_time = relative_position / 1000

                    # 计算裁剪后的总时长
                    cropped_duration_ms = end_time_ms - start_time_ms
                    total_time = cropped_duration_ms / 1000

                    self.time_label.setText(f"{self.format_time(current_time)} / {self.format_time(total_time)}")

                # 更新当前帧位置
                if hasattr(self, 'fps') and self.fps > 0:
                    self.current_frame = int((position / 1000) * self.fps)
        except Exception as e:
            print(f"更新播放位置失败: {e}")

    def update_playback_duration(self, duration):
        """更新播放时长"""
        try:
            if hasattr(self, 'playback_slider') and duration > 0:
                # 设置进度条范围（毫秒）
                self.playback_slider.setRange(0, duration)

                # 更新时间标签
                if hasattr(self, 'time_label'):
                    total_time = duration / 1000  # 转换为秒
                    self.time_label.setText(f"00:00 / {self.format_time(total_time)}")
        except Exception as e:
            print(f"更新播放时长失败: {e}")

    def handle_media_error(self, error):
        """处理媒体错误"""
        print(f"媒体播放错误: {error}")

    def on_playback_state_changed(self, state):
        """播放状态变化处理"""
        try:
            from PyQt6.QtMultimedia import QMediaPlayer

            if hasattr(self, 'play_pause_btn'):
                if state == QMediaPlayer.PlaybackState.PlayingState:
                    self.play_pause_btn.setText("暂停")
                elif state == QMediaPlayer.PlaybackState.PausedState:
                    self.play_pause_btn.setText("播放")
                elif state == QMediaPlayer.PlaybackState.StoppedState:
                    self.play_pause_btn.setText("播放")
        except Exception as e:
            print(f"播放状态变化处理失败: {e}")

    def set_playback_position(self, position):
        """设置播放位置"""
        try:
            if self.media_player.duration() > 0:
                # 直接设置位置（position已经是毫秒）
                self.media_player.setPosition(position)

                # 更新当前帧位置（用于截取功能）
                if hasattr(self, 'fps') and self.fps > 0:
                    self.current_frame = int((position / 1000) * self.fps)
        except Exception as e:
            print(f"设置播放位置失败: {e}")

    def on_slider_pressed(self):
        """滑块按下时的处理"""
        try:
            # 记录拖动前的播放状态
            self.was_playing_before_drag = (
                self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState
            )
        except Exception as e:
            print(f"滑块按下处理失败: {e}")

    def on_slider_released(self):
        """滑块释放时的处理"""
        try:
            # 如果拖动前正在播放，则继续播放
            if getattr(self, 'was_playing_before_drag', False):
                self.media_player.play()
                if hasattr(self, 'play_pause_btn'):
                    self.play_pause_btn.setText("暂停")
        except Exception as e:
            print(f"滑块释放处理失败: {e}")

    def update_media_player_range(self):
        """更新媒体播放器的播放范围"""
        try:
            if not hasattr(self, 'media_player') or not self.media_player:
                return

            # 计算播放范围（毫秒）
            start_time_ms = int((self.cropped_start_frames / self.fps) * 1000)
            end_time_ms = int(((self.total_frames - self.cropped_end_frames) / self.fps) * 1000)

            # 更新位置滑块的范围
            if hasattr(self, 'playback_slider'):
                self.playback_slider.setRange(start_time_ms, end_time_ms)

            # 根据当前帧计算对应的播放位置
            current_time_ms = int((self.current_frame / self.fps) * 1000)

            # 如果当前位置在有效范围内，设置到当前位置；否则设置到开始位置
            if start_time_ms <= current_time_ms <= end_time_ms:
                self.media_player.setPosition(current_time_ms)
                if hasattr(self, 'playback_slider'):
                    self.playback_slider.setValue(current_time_ms)
            else:
                self.media_player.setPosition(start_time_ms)
                if hasattr(self, 'playback_slider'):
                    self.playback_slider.setValue(start_time_ms)
        except Exception as e:
            print(f"更新媒体播放器范围失败: {e}")

    def _get_export_settings(self) -> Dict[str, Any]:
        """获取导出设置"""
        # 默认设置
        export_settings = {
            'output_format': 'MP4',  # 添加默认输出格式
            'width': 1440, 'height': 2560, 'fps': 30.0,
            'video_codec': 'H.264 (兼容性好)', 'quality_preset': '高质量 (推荐)',
            'use_gpu': True, 'bitrate_mode': 'VBR', 'target_bitrate': 8000,
            'crf_value': 23, 'preset': 'medium', 'audio_codec': 'AAC',
            'audio_bitrate': 128, 'resolution_preset': '自定义'
        }

        # 如果有用户设置的导出选项，使用它们
        if hasattr(self, 'export_settings_dict') and self.export_settings_dict:
            export_settings.update(self.export_settings_dict)
        elif hasattr(self, 'export_settings') and self.export_settings:
            try:
                user_settings = self.export_settings.get_settings()
                export_settings.update(user_settings)
            except:
                pass

        # 处理原始分辨率设置
        if export_settings.get('resolution_preset') == '原始分辨率':
            if hasattr(self, 'video_width') and hasattr(self, 'video_height') and self.video_width > 0 and self.video_height > 0:
                print(f"🎯 使用原始分辨率: {self.video_width}x{self.video_height}")
                export_settings['width'] = self.video_width
                export_settings['height'] = self.video_height
            else:
                print("⚠️ 无法获取视频原始分辨率，使用默认分辨率")
        else:
            # 使用原始导出设置，通过裁剪填充实现精确比例
            pass

        return export_settings

    def _adjust_resolution_for_aspect_ratio(self, export_settings: Dict[str, Any]) -> Dict[str, Any]:
        """智能调整分辨率以适应视频比例"""
        try:
            if not (hasattr(self, 'video_width') and hasattr(self, 'video_height') and
                   self.video_width > 0 and self.video_height > 0):
                print("⚠️ 无法获取视频分辨率信息，使用原始导出设置")
                return export_settings

            target_width = export_settings.get('width', 1440)
            target_height = export_settings.get('height', 2560)
            resolution_preset = export_settings.get('resolution_preset', '')

            # 计算源视频和目标的比例
            source_aspect = self.video_width / self.video_height
            target_aspect = target_width / target_height

            print(f"📐 源视频: {self.video_width}x{self.video_height} (比例: {source_aspect:.3f})")
            print(f"📐 目标设置: {target_width}x{target_height} (比例: {target_aspect:.3f})")

            # 定义比例容差（3%）
            aspect_tolerance = 0.03
            aspect_diff = abs(source_aspect - target_aspect) / target_aspect

            if aspect_diff <= aspect_tolerance:
                print(f"✅ 比例匹配 (差异: {aspect_diff:.1%})")
                return export_settings

            # 检查是否是接近标准比例的视频
            standard_ratios = {
                '9:16': 9/16,    # 0.5625
                '16:9': 16/9,    # 1.7778
                '4:3': 4/3,      # 1.3333
                '3:4': 3/4,      # 0.75
                '1:1': 1/1,      # 1.0
                '21:9': 21/9,    # 2.3333
            }

            # 找到最接近的标准比例
            closest_ratio = None
            min_diff = float('inf')

            for ratio_name, ratio_value in standard_ratios.items():
                diff = abs(source_aspect - ratio_value) / ratio_value
                if diff < min_diff:
                    min_diff = diff
                    closest_ratio = ratio_name

            # 如果源视频接近某个标准比例（差异小于12%），进行智能调整
            if min_diff <= 0.12 and closest_ratio:
                print(f"🎯 检测到接近 {closest_ratio} 比例的视频 (差异: {min_diff:.1%})")

                # 如果用户选择的是9:16比例，但源视频接近9:16
                if '9:16' in resolution_preset and closest_ratio == '9:16':
                    # 调整目标分辨率以匹配源视频的实际比例
                    adjusted_settings = self._adjust_to_source_aspect_ratio(export_settings, source_aspect)
                    if adjusted_settings:
                        return adjusted_settings

                # 如果用户选择的是16:9比例，但源视频接近16:9
                elif ('1920×1080' in resolution_preset or '1280×720' in resolution_preset or 'HD' in resolution_preset) and closest_ratio == '16:9':
                    adjusted_settings = self._adjust_to_source_aspect_ratio(export_settings, source_aspect)
                    if adjusted_settings:
                        return adjusted_settings

            print(f"⚠️ 比例差异较大 (差异: {aspect_diff:.1%})，将使用padding处理")
            return export_settings

        except Exception as e:
            print(f"❌ 比例调整失败: {e}")
            return export_settings

    def _adjust_to_source_aspect_ratio(self, export_settings: Dict[str, Any], source_aspect: float) -> Dict[str, Any]:
        """调整目标分辨率以匹配源视频比例"""
        try:
            target_width = export_settings.get('width', 1440)
            target_height = export_settings.get('height', 2560)
            resolution_preset = export_settings.get('resolution_preset', '')

            # 基于目标分辨率的较大边，计算匹配源比例的分辨率
            if '9:16' in resolution_preset:
                # 竖屏：保持宽度，调整高度
                new_width = target_width
                new_height = int(target_width / source_aspect)

                # 确保高度是偶数（视频编码要求）
                if new_height % 2 != 0:
                    new_height += 1

            elif ('1920×1080' in resolution_preset or '1280×720' in resolution_preset or 'HD' in resolution_preset):
                # 横屏：保持宽度，调整高度
                new_width = target_width
                new_height = int(target_width / source_aspect)

                # 确保高度是偶数
                if new_height % 2 != 0:
                    new_height += 1
            else:
                # 其他情况，保持原设置
                return None

            # 检查调整后的分辨率是否合理
            if new_width < 100 or new_height < 100 or new_width > 7680 or new_height > 4320:
                print(f"⚠️ 调整后分辨率不合理: {new_width}x{new_height}")
                return None

            # 创建调整后的设置
            adjusted_settings = export_settings.copy()
            adjusted_settings['width'] = new_width
            adjusted_settings['height'] = new_height

            print(f"🔧 智能调整分辨率: {target_width}x{target_height} → {new_width}x{new_height}")
            print(f"   源比例: {source_aspect:.4f}, 新比例: {new_width/new_height:.4f}")

            return adjusted_settings

        except Exception as e:
            print(f"❌ 分辨率调整失败: {e}")
            return None

    def _get_ffmpeg_path(self) -> str:
        """获取FFmpeg路径"""
        import sys

        # 获取程序运行的基础路径
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的路径
            base_path = sys._MEIPASS
        else:
            # 开发环境路径
            base_path = os.path.dirname(os.path.dirname(__file__))

        # 检查多个可能的FFmpeg位置
        possible_paths = [
            # 打包后可能的位置
            os.path.join(base_path, "bin", "ffmpeg.exe"),
            os.path.join(base_path, "ffmpeg.exe"),
            # 开发环境位置
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "bin", "ffmpeg.exe"),
            # 当前目录
            os.path.join(os.getcwd(), "bin", "ffmpeg.exe"),
            os.path.join(os.getcwd(), "ffmpeg.exe"),
        ]

        # 检查每个可能的路径
        for path in possible_paths:
            if os.path.exists(path):
                print(f"Found FFmpeg at: {path}")
                return path

        # 检查系统PATH中的ffmpeg
        import shutil
        system_ffmpeg = shutil.which("ffmpeg")
        if system_ffmpeg:
            print(f"Using system FFmpeg: {system_ffmpeg}")
            return system_ffmpeg

        # 如果都找不到，返回默认值并打印警告
        print("Warning: FFmpeg not found, using default 'ffmpeg'")
        return "ffmpeg"

    def _get_best_encoder(self, ffmpeg_path: str) -> str:
        """获取最佳编码器"""
        try:
            # 获取用户的GPU加速设置
            export_settings = self._get_export_settings()
            use_gpu = export_settings.get('use_gpu', False)

            print(f"GPU加速设置: {use_gpu}")

            # 如果用户禁用了GPU加速，直接返回软件编码器
            if not use_gpu:
                print("用户禁用GPU加速，使用软件编码器")
                return 'libx264'

            # 检查可用的编码器
            result = subprocess.run([ffmpeg_path, '-encoders'],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                encoders = result.stdout

                # 优先级：NVIDIA > Intel > 软件编码
                if 'h264_nvenc' in encoders:
                    print("使用NVIDIA硬件编码器: h264_nvenc")
                    return 'h264_nvenc'
                elif 'h264_qsv' in encoders:
                    print("使用Intel硬件编码器: h264_qsv")
                    return 'h264_qsv'
                else:
                    print("硬件编码器不可用，使用软件编码器: libx264")
                    return 'libx264'
            else:
                print("无法检查编码器，使用默认软件编码器")
                return 'libx264'

        except Exception as e:
            print(f"检查编码器失败: {e}")
            return 'libx264'

    def _get_video_duration(self, video_path: str) -> float:
        """使用ffprobe获取视频时长（秒）"""
        try:
            ffprobe_path = os.path.join(os.path.dirname(__file__), '..', 'bin', 'ffprobe.exe')
            if not os.path.exists(ffprobe_path):
                ffprobe_path = 'ffprobe'  # 回退到系统PATH

            cmd = [
                ffprobe_path,
                '-v', 'quiet',
                '-show_entries', 'format=duration',
                '-of', 'csv=p=0',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                duration = float(result.stdout.strip())
                print(f"视频时长: {duration:.2f}秒")
                return duration
            else:
                print(f"获取视频时长失败: {result.stderr}")
                return 0.0

        except Exception as e:
            print(f"获取视频时长异常: {e}")
            return 0.0

    def _run_ffmpeg_with_progress(self, cmd: list, start_progress: int, end_progress: int, timeout: int = 180) -> int:
        """运行FFmpeg并实时更新进度"""
        try:
            import re
            import threading

            # 启动FFmpeg进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            # 获取视频总时长
            total_duration = self.total_frames / self.fps if hasattr(self, 'total_frames') and hasattr(self, 'fps') and self.fps > 0 else 0

            def read_progress():
                """读取FFmpeg进度输出"""
                while True:
                    line = process.stderr.readline()
                    if not line:
                        break

                    # 解析时间进度 (格式: time=00:01:23.45)
                    time_match = re.search(r'time=(\d+):(\d+):(\d+)\.(\d+)', line)
                    if time_match and total_duration > 0:
                        hours = int(time_match.group(1))
                        minutes = int(time_match.group(2))
                        seconds = int(time_match.group(3))
                        milliseconds = int(time_match.group(4))

                        current_time = hours * 3600 + minutes * 60 + seconds + milliseconds / 100
                        progress_percent = min(current_time / total_duration, 1.0)

                        # 映射到指定的进度范围
                        actual_progress = int(start_progress + progress_percent * (end_progress - start_progress))
                        self.progress_updated.emit(actual_progress)

            # 启动进度读取线程
            progress_thread = threading.Thread(target=read_progress)
            progress_thread.daemon = True
            progress_thread.start()

            # 等待进程完成
            try:
                process.wait(timeout=timeout)
                return process.returncode
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"FFmpeg执行超时 ({timeout}秒)")
                return -1

        except Exception as e:
            print(f"运行FFmpeg失败: {e}")
            return -1

    def _update_export_progress(self, progress: int):
        """更新导出进度"""
        try:
            # 显示并更新进度条
            if hasattr(self, 'export_progress'):
                self.export_progress.setVisible(True)
                self.export_progress.setValue(progress)
            print(f"导出进度: {progress}%")
        except Exception as e:
            print(f"更新导出进度失败: {e}")

    def _on_export_finished(self, output_path: str, success: bool, error_msg: str):
        """导出完成处理"""
        try:
            # 恢复导出按钮
            self.export_btn.setEnabled(True)
            self.export_btn.setText("导出视频")

            # 隐藏进度条
            if hasattr(self, 'export_progress'):
                self.export_progress.setVisible(False)
                self.export_progress.setValue(0)

            if success:
                print(f"✅ 视频导出成功！保存位置: {output_path}")
            else:
                QMessageBox.critical(self, Constants.MSG_ERROR,
                                   f"视频导出失败！\n错误信息: {error_msg}")
        except Exception as e:
            print(f"导出完成处理失败: {e}")

    def _get_paths_config_file(self) -> str:
        """获取路径配置文件路径"""
        try:
            # 保存到项目文件夹的config目录
            import os
            from pathlib import Path

            # 获取当前模块的目录（modules文件夹）
            current_dir = Path(__file__).parent
            # 回到项目根目录
            project_root = current_dir.parent
            # 创建config目录
            config_dir = project_root / 'config'

            # 确保目录存在
            config_dir.mkdir(exist_ok=True)

            config_file = config_dir / 'paths.json'
            print(f"📁 配置文件路径: {config_file}")

            return str(config_file)
        except Exception as e:
            print(f"获取配置文件路径失败: {e}")
            return 'paths.json'  # 回退到当前目录

    def _load_saved_paths(self):
        """加载保存的路径"""
        try:
            import json
            config_file = self._get_paths_config_file()

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    paths = json.load(f)

                self.last_video_path = paths.get('last_video_path', '')
                self.last_image_path = paths.get('last_image_path', '')
                self.last_export_path = paths.get('last_export_path', '')

                print(f"📁 加载保存的路径:")
                print(f"  视频路径: {self.last_video_path}")
                print(f"  图片路径: {self.last_image_path}")
                print(f"  导出路径: {self.last_export_path}")
            else:
                print("📁 首次使用，使用默认路径")
                # 设置默认路径为用户的桌面或文档目录
                self._set_default_paths()

        except Exception as e:
            print(f"加载路径配置失败: {e}")
            self._set_default_paths()

    def _set_default_paths(self):
        """设置默认路径"""
        try:
            from pathlib import Path

            # 默认使用用户的桌面目录
            desktop = Path.home() / 'Desktop'
            if desktop.exists():
                default_path = str(desktop)
            else:
                # 回退到用户主目录
                default_path = str(Path.home())

            self.last_video_path = default_path
            self.last_image_path = default_path
            self.last_export_path = default_path

            print(f"📁 设置默认路径: {default_path}")

        except Exception as e:
            print(f"设置默认路径失败: {e}")
            # 最终回退
            self.last_video_path = ""
            self.last_image_path = ""
            self.last_export_path = ""

    def _save_paths(self):
        """保存当前路径"""
        try:
            import json
            config_file = self._get_paths_config_file()

            paths = {
                'last_video_path': self.last_video_path,
                'last_image_path': self.last_image_path,
                'last_export_path': self.last_export_path
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(paths, f, ensure_ascii=False, indent=2)

            print(f"💾 路径已保存到: {config_file}")

        except Exception as e:
            print(f"保存路径配置失败: {e}")
