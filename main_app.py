import sys
import importlib
import logging
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QStackedWidget, QPushButton,
                             QSizePolicy, QFrame, QLabel, QMessageBox,QDialog)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QAction

# 配置日志记录
logging.basicConfig(
    filename='app_debug.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建自定义异常钩子
def exception_hook(exc_type, exc_value, exc_traceback):
    """捕获未处理的异常并记录到日志"""
    import traceback

    # 记录详细的异常信息
    logger.error("未处理的异常:", exc_info=(exc_type, exc_value, exc_traceback))

    # 获取详细的错误信息
    error_details = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))

    # 显示错误消息框，但不退出程序
    try:
        error_msg = QMessageBox()
        error_msg.setIcon(QMessageBox.Icon.Critical)
        error_msg.setText(f"程序发生错误，但将继续运行:\n{str(exc_value)}")
        error_msg.setDetailedText(error_details)
        error_msg.setWindowTitle("错误 - 程序继续运行")
        error_msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        error_msg.exec()
    except:
        # 如果连错误消息框都无法显示，至少打印到控制台
        print(f"严重错误: {exc_value}")
        print(error_details)

    # 不调用sys.exit()，让程序继续运行
    
# 设置全局异常钩子
sys.excepthook = exception_hook

class VideoProcessingApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("寒星 v1.0.2")
        self.setMinimumSize(1200, 800)
        self.module_instances = {}  # 缓存模块实例
        
        # 模块路径映射
        self.module_paths = {
            0: "modules.video_split",      # 视频分割
            1: "modules.video_transcode",  # 视频转码
            2: "modules.cover_edit",       # 封面编辑
            3: "modules.audio_extract"     # 音频提取
        }
        
        self.init_ui()

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        try:
            # 检查是否有批量处理正在进行
            current_module = self.stacked_widget.currentWidget()
            if hasattr(current_module, 'batch_mode_enabled') and current_module.batch_mode_enabled:
                if hasattr(current_module, 'current_processing') and current_module.current_processing:
                    # 有批量处理正在进行，询问用户
                    reply = QMessageBox.question(
                        self,
                        '确认退出',
                        '批量处理正在进行中，确定要退出吗？\n退出将中断当前处理。',
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.No
                    )

                    if reply == QMessageBox.StandardButton.No:
                        event.ignore()  # 取消关闭
                        return
                    else:
                        # 用户确认退出，停止批量处理
                        if hasattr(current_module, 'stop_batch_monitoring'):
                            current_module.stop_batch_monitoring()

            # 正常关闭
            event.accept()

        except Exception as e:
            logger.error(f"关闭窗口时发生错误: {e}")
            # 即使出错也允许关闭
            event.accept()

    def init_ui(self):
        # 主窗口背景色
        self.setStyleSheet("""
            background-color: #181A1F;
            color: #D0D0D0;
            font-family: 'Segoe UI', Arial, sans-serif;
        """)
        
        # 创建主容器
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建标题栏
        self.create_title_bar(main_layout)
        
        # 创建功能区域
        self.create_function_area(main_layout)
        
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        # 初始化时显示第一个模块并更新按钮样式
        self.show_module(0)
    
    def create_title_bar(self, parent_layout):
        # 标题栏容器
        title_bar = QFrame()
        title_bar.setMinimumHeight(60)
        title_bar.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        title_bar.setStyleSheet("background-color: #24262B; border-bottom: 1px solid #303238;")
        
        # 标题栏布局
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(10, 0, 10, 0)
        title_layout.setSpacing(5)
        
        # 功能按钮
        self.split_btn = self.create_menu_button("视频分割", "#61AFEF")
        self.split_btn.clicked.connect(lambda: self.show_module(0))
        
        self.transcode_btn = self.create_menu_button("视频转码", "#98C379")
        self.transcode_btn.clicked.connect(lambda: self.show_module(1))
        
        # 封面编辑按钮 (替换原来的视频合并按钮)
        self.cover_edit_btn = self.create_menu_button("封面编辑", "#D19A66")  # 使用新颜色
        self.cover_edit_btn.clicked.connect(lambda: self.show_module(2))
        
        self.extract_btn = self.create_menu_button("音频提取", "#C678DD")
        self.extract_btn.clicked.connect(lambda: self.show_module(3))


        
        # 添加到标题栏
        title_layout.addStretch(1)
        title_layout.addWidget(self.split_btn)
        title_layout.addSpacing(10)
        title_layout.addWidget(self.transcode_btn)
        title_layout.addSpacing(10)
        title_layout.addWidget(self.cover_edit_btn)  # 使用新按钮
        title_layout.addSpacing(10)
        title_layout.addWidget(self.extract_btn)
        title_layout.addStretch(1)
        
        parent_layout.addWidget(title_bar)
    
    def create_menu_button(self, text, color):
        btn = QPushButton(text)
        btn.setMinimumSize(120, 45)
        btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        
        font = QFont("Microsoft YaHei UI", 14)
        font.setBold(True)
        btn.setFont(font)
        
        btn.setStyleSheet(f"""
            QPushButton {{
                color: {color};
                background-color: transparent;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: #2C313A;
            }}
        """)
        return btn
    
    def create_function_area(self, parent_layout):
        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setStyleSheet("background-color: #181A1F;")
        self.stacked_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # 添加4个占位符
        for i in range(4):
            placeholder = self.create_placeholder(i)
            self.stacked_widget.addWidget(placeholder)
        
        parent_layout.addWidget(self.stacked_widget)
    
    def create_placeholder(self, index):
        """创建模块加载前的占位内容"""
        # 更新文本和颜色
        colors = ["#61AFEF", "#98C379", "#D19A66", "#C678DD", "#E06C75"]  # 添加模块化版本颜色
        texts = ["视频分割", "视频转码", "封面编辑", "音频提取", "封面编辑(优化版)"]  # 更新文本
        
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        title = QLabel(f"{texts[index]}模块")
        title.setStyleSheet(f"color: {colors[index]}; font-size: 24px; font-weight: bold;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        desc = QLabel("加载中...")
        desc.setStyleSheet("color: #ABB2BF; font-size: 16px;")
        desc.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        layout.addStretch()
        layout.addWidget(title)
        layout.addWidget(desc)
        layout.addStretch()
        
        return widget
    
    def load_module(self, index):
        """动态加载并显示指定模块"""
        # 如果模块已经加载，直接显示
        if index in self.module_instances:
            self.stacked_widget.setCurrentIndex(index)
            return
        
        try:
            # 动态导入模块
            module = importlib.import_module(self.module_paths[index])
            
            # 获取模块类名（约定为模块名的驼峰形式 + "Module"）
            module_name = self.module_paths[index].split('.')[-1]

            # 特殊处理封面编辑模块
            if self.module_paths[index] == "modules.cover_edit":
                class_name = "CoverEditOptimizedModule"  # 使用优化版本的类名
            else:
                class_name = ''.join(word.capitalize() for word in module_name.split('_')) + "Module"
            
            # 创建模块实例
            module_class = getattr(module, class_name)
            instance = module_class(self)
            
            # 替换堆叠窗口中的占位符
            self.stacked_widget.insertWidget(index, instance)
            self.stacked_widget.removeWidget(self.stacked_widget.widget(index + 1))
            
            # 缓存实例
            self.module_instances[index] = instance
            self.stacked_widget.setCurrentIndex(index)
            
        except Exception as e:
            # 显示错误信息
            error_widget = QWidget()
            layout = QVBoxLayout(error_widget)
            
            title = QLabel(f"模块加载错误")
            title.setStyleSheet("color: #E06C75; font-size: 24px; font-weight: bold;")
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            error_msg = QLabel(f"无法加载模块: {str(e)}")
            error_msg.setStyleSheet("color: #ABB2BF; font-size: 16px;")
            error_msg.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            layout.addStretch()
            layout.addWidget(title)
            layout.addWidget(error_msg)
            layout.addStretch()
            
            self.stacked_widget.insertWidget(index, error_widget)
            self.stacked_widget.removeWidget(self.stacked_widget.widget(index + 1))
            
            logger.error(f"模块加载错误: {str(e)}", exc_info=True)
    
    def show_module(self, index):
        """显示指定模块"""
        self.load_module(index)
        self.update_button_style(index)
    
    def update_button_style(self, active_index):
        # 更新按钮列表
        buttons = [
            self.split_btn,
            self.transcode_btn,
            self.cover_edit_btn,
            self.extract_btn
        ]
        colors = ["#61AFEF", "#98C379", "#D19A66", "#C678DD"]
        
        for i, btn in enumerate(buttons):
            if i == active_index:
                # 选中状态：背景色为对应颜色，文字白色
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {colors[i]};
                        color: white;
                        font-weight: bold;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 16px;
                    }}
                """)
            else:
                # 非选中状态：背景透明，文字为对应颜色
                btn.setStyleSheet(f"""
                    QPushButton {{
                        color: {colors[i]};
                        background-color: transparent;
                        border: none;
                        font-weight: medium;
                        border-radius: 4px;
                        padding: 8px 16px;
                    }}
                    QPushButton:hover {{
                        background-color: #2C313A;
                    }}
                """)

if __name__ == "__main__":
    # 确保只有一个QApplication实例
    if not QApplication.instance():
        app = QApplication(sys.argv)
    else:
        app = QApplication.instance()
    
    app.setStyle("Fusion")
    
    # 创建并显示主窗口
    window = VideoProcessingApp()
    window.show()
    
    # 启动应用 (PyQt6 使用 exec() 而不是 exec_())
    sys.exit(app.exec())
