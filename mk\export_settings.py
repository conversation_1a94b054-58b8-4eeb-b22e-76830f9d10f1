"""
导出设置模块 - 参考video_transcode模块的转码设置组
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
    QSpinBox, QPushButton, QGridLayout, QCheckBox,
    QDoubleSpinBox, QSlider, QLineEdit
)
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QFont


class VideoExportSettings(QWidget):
    """视频导出设置类 - 完整的视频导出参数配置"""
    settings_updated = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频导出设置")
        self.setFixedSize(360, 120)  # 设置固定大小
        # 移除固定位置设置，由调用方控制位置

        # 设置窗口关闭时隐藏而不是退出应用
        self.setAttribute(Qt.WidgetAttribute.WA_QuitOnClose, False)

        # 设置为独立窗口
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowStaysOnTopHint)

        # 默认设置 - 参考video_transcode的完整选项
        self.settings = {
            # 基础设置
            'width': 1440,
            'height': 2560,
            'fps': 30.0,
            'start_time': 0.0,
            'end_time': 60.0,

            # 输出格式
            'output_format': 'MP4',

            # 分辨率预设
            'resolution_preset': '1440×2560 (9:16 竖屏)',

            # 视频编码
            'video_codec': 'H.264 (兼容性好)',

            # 质量预设
            'quality_preset': '高质量 (推荐)',

            # 硬件加速
            'use_gpu': True,

            # 高级设置
            'bitrate_mode': 'VBR',  # VBR, CBR, CRF
            'target_bitrate': 20000,  # kbps - 更新为1440x2560的推荐码率
            'max_bitrate': 30000,    # kbps - 相应调整最大码率
            'crf_value': 23,         # 0-51
            'preset': 'medium',      # ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow

            # 音频设置
            'audio_codec': 'AAC',
            'audio_bitrate': 128,    # kbps
            'audio_sample_rate': 44100,
        }
        self.init_ui()

    def init_ui(self):
        """初始化用户界面 - 参考video_transcode的完整布局"""
        self.setStyleSheet(self.get_style())

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 17, 5, 17)  # 左右边距设为5px，上下保持17px
        main_layout.setSpacing(12)  # 保持12px间距

        # 直接使用网格布局，移除组框
        unified_layout = QGridLayout()
        unified_layout.setContentsMargins(0, 7, 0, 7)  # 左右边距设为0px，上下保持7px
        unified_layout.setSpacing(9)  # 保持9px间距

        # 第一行：输出格式 + 分辨率预设
        unified_layout.addWidget(QLabel("输出格式:"), 0, 0)
        self.format_combo = QComboBox()
        self.format_combo.addItems([
            "MP4", "MKV", "MOV", "AVI", "WMV", "M4V",
            "MPEG", "VOB", "WEBM", "OGV", "3GP", "FLV", "TS"
        ])
        self.format_combo.setCurrentText(self.settings['output_format'])
        unified_layout.addWidget(self.format_combo, 0, 1)

        unified_layout.addWidget(QLabel("分辨率:"), 0, 2)
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems([
            "原始分辨率",
            "4K UHD, 3840×2160",
            "2K QHD, 2560×1440",
            "HD, 1920×1080",
            "HD, 1280×720",
            # 9:16 竖屏分辨率
            "2160×3840 (9:16 竖屏)",
            "1440×2560 (9:16 竖屏)",
            "1080×1920 (9:16 竖屏)",
            "720×1280 (9:16 竖屏)"
        ])
        self.resolution_combo.setCurrentText(self.settings['resolution_preset'])
        self.resolution_combo.currentTextChanged.connect(self.on_resolution_changed)
        unified_layout.addWidget(self.resolution_combo, 0, 3)

        # 第二行：视频编码 + 预设质量
        unified_layout.addWidget(QLabel("视频编码:"), 1, 0)
        self.codec_combo = QComboBox()
        self.codec_combo.addItems([
            "H.264 (兼容性好)",
            "H.265 (高效压缩)",
            "AV1 (最新标准)",
            "VP9 (Web优化)",
            "MPEG-2 (DVD质量)",
            "MPEG-4 (通用编码)",
            "WMV (Windows媒体)",
            "Theora (开源编码)",
            "原始编码"
        ])
        self.codec_combo.setCurrentText(self.settings['video_codec'])
        unified_layout.addWidget(self.codec_combo, 1, 1)

        unified_layout.addWidget(QLabel("预设质量:"), 1, 2)
        self.quality_preset_combo = QComboBox()
        self.quality_preset_combo.addItems([
            "超高质量 (最佳画质)",
            "高质量 (推荐)",
            "中等质量 (平衡)",
            "低质量 (小文件)",
            "快速编码 (省时间)"
        ])
        self.quality_preset_combo.setCurrentText("高质量 (推荐)")
        unified_layout.addWidget(self.quality_preset_combo, 1, 3)

        # 保留变量以避免错误，但隐藏控件
        self.width_spinbox = QSpinBox()
        self.width_spinbox.setRange(1, 7680)
        self.width_spinbox.setValue(self.settings['width'])
        self.width_spinbox.hide()

        self.height_spinbox = QSpinBox()
        self.height_spinbox.setRange(1, 4320)
        self.height_spinbox.setValue(self.settings['height'])
        self.height_spinbox.hide()

        # 保留原有的质量预设控件但隐藏，避免代码错误
        self.quality_combo = QComboBox()
        self.quality_combo.addItems([
            "超高质量 (文件最大)",
            "高质量 (推荐)",
            "中等质量",
            "低质量 (文件最小)",
            "恒定质量 (CRF 18)",
            "快速编码 (低CPU占用)",
            "慢速编码 (高压缩率)"
        ])
        self.quality_combo.setCurrentText(self.settings['quality_preset'])
        self.quality_combo.hide()

        # 保留帧率控件但隐藏
        self.fps_combo = QComboBox()
        self.fps_combo.addItems([
            "23.976", "24", "25", "29.97", "30",
            "48", "50", "59.94", "60", "120"
        ])
        self.fps_combo.setCurrentText("30")
        self.fps_combo.setEditable(True)
        self.fps_combo.hide()

        # GPU加速选项暂时保留在这里，稍后会移到组外
        self.gpu_checkbox = QCheckBox("启用GPU硬件加速")
        self.gpu_checkbox.setChecked(self.settings['use_gpu'])

        # 保留原有的控件但隐藏，避免代码错误
        self.bitrate_mode_combo = QComboBox()
        self.bitrate_mode_combo.addItems(["VBR (可变码率)", "CBR (恒定码率)", "CRF (恒定质量)"])
        self.bitrate_mode_combo.setCurrentText("VBR (可变码率)")
        self.bitrate_mode_combo.hide()

        self.bitrate_spinbox = QSpinBox()
        self.bitrate_spinbox.setRange(500, 50000)
        self.bitrate_spinbox.setValue(self.settings['target_bitrate'])
        self.bitrate_spinbox.hide()

        self.crf_spinbox = QSpinBox()
        self.crf_spinbox.setRange(0, 51)
        self.crf_spinbox.setValue(self.settings['crf_value'])
        self.crf_spinbox.hide()

        self.preset_combo = QComboBox()
        self.preset_combo.addItems([
            "ultrafast", "superfast", "veryfast", "faster",
            "fast", "medium", "slow", "slower", "veryslow"
        ])
        self.preset_combo.setCurrentText(self.settings['preset'])
        self.preset_combo.hide()

        # 将网格布局添加到主布局
        main_layout.addLayout(unified_layout)

        # 底部控制区域 - GPU加速、重置默认、应用设置在同一行
        bottom_layout = QHBoxLayout()

        # GPU加速复选框 - 进一步缩小样式
        self.gpu_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 10px;
                padding: 2px;
            }
            QCheckBox::indicator {
                width: 12px;
                height: 12px;
            }
        """)
        bottom_layout.addWidget(self.gpu_checkbox)

        # 添加弹性空间
        bottom_layout.addStretch()

        # 统一的按钮样式 - 进一步缩小5px
        button_style = """
            QPushButton {
                background-color: #f0f0f0;
                color: #333;
                border: 1px solid #ccc;
                border-radius: 2px;
                padding: 3px 8px;
                font-size: 10px;
                min-width: 60px;
                max-height: 23px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border-color: #999;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """

        # 应用设置按钮样式（绿色主题，进一步缩小5px）
        apply_button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: 1px solid #4CAF50;
                border-radius: 2px;
                padding: 3px 8px;
                font-size: 10px;
                min-width: 60px;
                max-height: 23px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
                border-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """

        # 重置按钮
        reset_btn = QPushButton("重置默认")
        reset_btn.setStyleSheet(button_style)
        reset_btn.clicked.connect(self.reset_to_defaults)
        bottom_layout.addWidget(reset_btn)

        # 应用按钮
        apply_btn = QPushButton("应用设置")
        apply_btn.setStyleSheet(apply_button_style)
        apply_btn.clicked.connect(self.apply_settings)
        bottom_layout.addWidget(apply_btn)

        main_layout.addLayout(bottom_layout)

        # 初始化UI状态
        self.on_resolution_changed(self.resolution_combo.currentText())

    def get_style(self):
        """获取样式表"""
        return """
            QWidget {
                background-color: #2B2B2B;
                color: #FFFFFF;
                font-family: 'Segoe UI', Arial, sans-serif;
            }

            QComboBox, QSpinBox, QDoubleSpinBox {
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 2px;
                background-color: #3C3C3C;
                min-height: 15px;
                font-size: 11px;
            }
            QComboBox:hover, QSpinBox:hover, QDoubleSpinBox:hover {
                border-color: #4CAF50;
            }
            QComboBox::drop-down {
                border: none;
                width: 0px;
            }
            QComboBox::down-arrow {
                image: none;
                width: 0px;
                height: 0px;
            }
            QPushButton {
                background-color: #555555;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #666666;
            }
            QCheckBox {
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #555555;
                border-radius: 3px;
                background-color: #3C3C3C;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border-color: #4CAF50;
            }
            QLabel {
                color: #CCCCCC;
                font-size: 11px;
            }
        """

    def on_resolution_changed(self, text=None):
        """分辨率预设改变时的处理"""
        preset = text if text is not None else self.resolution_combo.currentText()

        # 分辨率映射表 - 参考video_transcode
        resolution_map = {
            "2160p (4K UHD, 3840×2160)": (3840, 2160),
            "1440p (2K QHD, 2560×1440)": (2560, 1440),
            "1080p (Full HD, 1920×1080)": (1920, 1080),
            "720p (HD, 1280×720)": (1280, 720),
            "2160×3840 (9:16 竖屏)": (2160, 3840),
            "1440×2560 (9:16 竖屏)": (1440, 2560),
            "1080×1920 (9:16 竖屏)": (1080, 1920),
            "720×1280 (9:16 竖屏)": (720, 1280),
        }

        # 分辨率对应的推荐码率映射表（kbps）
        bitrate_map = {
            "2160p (4K UHD, 3840×2160)": 25000,      # 4K横屏：25Mbps
            "1440p (2K QHD, 2560×1440)": 16000,      # 2K横屏：16Mbps
            "1080p (Full HD, 1920×1080)": 8000,      # 1080p横屏：8Mbps
            "720p (HD, 1280×720)": 5000,             # 720p横屏：5Mbps
            "2160×3840 (9:16 竖屏)": 30000,          # 4K竖屏：30Mbps（更高像素密度）
            "1440×2560 (9:16 竖屏)": 20000,          # 2K竖屏：20Mbps
            "1080×1920 (9:16 竖屏)": 10000,          # 1080p竖屏：10Mbps
            "720×1280 (9:16 竖屏)": 6000,            # 720p竖屏：6Mbps
        }

        if preset in resolution_map:
            width, height = resolution_map[preset]
            self.width_spinbox.setValue(width)
            self.height_spinbox.setValue(height)

            # 自动设置匹配的码率
            if preset in bitrate_map:
                recommended_bitrate = bitrate_map[preset]
                self.bitrate_spinbox.setValue(recommended_bitrate)
                print(f"分辨率 {preset} -> 推荐码率: {recommended_bitrate}kbps")

        # "原始分辨率"保持当前数值
        elif preset == "原始分辨率":
            pass  # 不修改分辨率数值

    def reset_to_defaults(self):
        """重置为默认设置"""
        # 重置所有控件到默认值
        self.format_combo.setCurrentText("MP4")
        self.fps_combo.setCurrentText("30")
        self.resolution_combo.setCurrentText("1440×2560 (9:16 竖屏)")
        self.codec_combo.setCurrentText("H.264 (兼容性好)")
        self.quality_preset_combo.setCurrentText("高质量 (推荐)")
        self.gpu_checkbox.setChecked(True)
        self.bitrate_mode_combo.setCurrentText("VBR (可变码率)")
        self.bitrate_spinbox.setValue(20000)  # 更新为1440x2560的推荐码率
        self.crf_spinbox.setValue(23)
        self.preset_combo.setCurrentText("medium")

        # 触发分辨率更新
        self.on_resolution_changed()

    def apply_settings(self):
        """应用设置并发送信号"""
        # 收集所有设置
        settings = self.get_settings()

        # 更新内部设置
        self.settings.update(settings)

        # 发送更新信号
        self.settings_updated.emit(settings)

        # 隐藏窗口而不是关闭
        self.hide()

    def get_settings(self):
        """获取当前所有设置"""
        # 解析码率模式
        bitrate_mode_text = self.bitrate_mode_combo.currentText()
        if "VBR" in bitrate_mode_text:
            bitrate_mode = "VBR"
        elif "CBR" in bitrate_mode_text:
            bitrate_mode = "CBR"
        else:
            bitrate_mode = "CRF"

        return {
            # 基础设置
            'width': self.width_spinbox.value(),
            'height': self.height_spinbox.value(),
            'fps': float(self.fps_combo.currentText()),
            'start_time': self.settings.get('start_time', 0.0),
            'end_time': self.settings.get('end_time', 60.0),

            # 输出格式
            'output_format': self.format_combo.currentText(),

            # 分辨率预设
            'resolution_preset': self.resolution_combo.currentText(),

            # 视频编码
            'video_codec': self.codec_combo.currentText(),

            # 质量预设
            'quality_preset': self.quality_preset_combo.currentText(),

            # 硬件加速
            'use_gpu': self.gpu_checkbox.isChecked(),

            # 高级设置
            'bitrate_mode': bitrate_mode,
            'target_bitrate': self.bitrate_spinbox.value(),
            'max_bitrate': int(self.bitrate_spinbox.value() * 1.5),  # 最大码率为目标码率的1.5倍
            'crf_value': self.crf_spinbox.value(),
            'preset': self.preset_combo.currentText(),

            # 音频设置（保持默认值）
            'audio_codec': 'AAC',
            'audio_bitrate': 128,
            'audio_sample_rate': 44100,
        }

    def set_values(self, values):
        """设置控件值"""
        if 'fps' in values:
            fps_text = str(values['fps'])
            # 尝试在下拉列表中找到匹配项
            index = self.fps_combo.findText(fps_text)
            if index >= 0:
                self.fps_combo.setCurrentIndex(index)
            else:
                # 如果没找到，直接设置文本（因为是可编辑的）
                self.fps_combo.setCurrentText(fps_text)
        if 'width' in values:
            self.width_spinbox.setValue(values['width'])
        if 'height' in values:
            self.height_spinbox.setValue(values['height'])
        if 'start_time' in values:
            self.settings['start_time'] = values['start_time']
        if 'end_time' in values:
            self.settings['end_time'] = values['end_time']
        if 'batch_mode' in values and values['batch_mode']:
            # 批量模式提示
            self.setWindowTitle("视频导出设置 - 批量处理模式")

        # 更新内部设置
        self.settings.update(values)
