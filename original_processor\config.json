{"default_settings": {"video_effects": {"mirror_horizontal": false, "mirror_vertical": false, "brightness": 0, "contrast": 0, "saturation": 0, "crop_enabled": false, "crop_x": 0, "crop_y": 0, "crop_width": 100, "crop_height": 100, "watermark_enabled": false, "watermark_path": "", "watermark_position": "bottom-right", "watermark_opacity": 50}, "audio_effects": {"audio_speed": 100, "audio_pitch": 0, "background_music_enabled": false, "background_music_path": "", "background_volume": 20, "noise_reduction": false}, "timeline_effects": {"video_speed": 100, "random_cuts": false, "cuts_count": 3, "transition_effects": false, "transition_type": "fade"}, "output_settings": {"output_format": "mp4", "output_quality": "高质量", "output_resolution": "保持原始"}}, "presets": {"light": {"name": "轻度处理", "description": "微调参数，保持原始质量", "settings": {"brightness": 5, "contrast": 3, "saturation": 2, "audio_speed": 102, "audio_pitch": 1, "video_speed": 101}}, "medium": {"name": "中度处理", "description": "适中的修改，平衡效果和质量", "settings": {"brightness": 10, "contrast": 8, "saturation": 5, "mirror_horizontal": true, "audio_speed": 105, "audio_pitch": 2, "video_speed": 103, "random_cuts": true, "cuts_count": 2}}, "heavy": {"name": "重度处理", "description": "大幅修改，最大化处理效果", "settings": {"brightness": 15, "contrast": 12, "saturation": 10, "mirror_horizontal": true, "mirror_vertical": true, "audio_speed": 110, "audio_pitch": 3, "video_speed": 107, "random_cuts": true, "cuts_count": 5, "transition_effects": true, "noise_reduction": true}}}, "platform_presets": {"kuaishou": {"name": "快手优化", "settings": {"mirror_horizontal": true, "brightness": 12, "contrast": 8, "saturation": 6, "audio_speed": 105, "video_speed": 103, "output_quality": "高质量", "output_resolution": "1080p"}}, "douyin": {"name": "抖音优化", "settings": {"brightness": 8, "contrast": 10, "saturation": 5, "audio_pitch": 1, "video_speed": 102, "transition_effects": true, "output_quality": "高质量", "output_resolution": "1080p"}}, "bilibili": {"name": "B站优化", "settings": {"brightness": 6, "contrast": 5, "saturation": 3, "audio_speed": 102, "video_speed": 101, "noise_reduction": true, "output_quality": "超高质量", "output_resolution": "1080p"}}}, "advanced_settings": {"processing": {"max_threads": 4, "memory_limit_mb": 2048, "temp_dir": "temp", "cleanup_temp": true}, "ffmpeg": {"hardware_acceleration": false, "gpu_type": "auto", "custom_args": []}, "quality": {"crf_values": {"低质量": 28, "中等质量": 23, "高质量": 18, "超高质量": 15}, "bitrate_multiplier": 1.0}}, "ui_settings": {"remember_last_settings": true, "auto_save_presets": true, "show_advanced_options": false, "preview_enabled": true}}