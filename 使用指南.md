# 视频原创处理模块 - 使用指南

## 🚀 快速开始

### 1. 启动程序
运行 `main_app.py`，程序将显示主界面。

### 2. 进入原创处理模块
在主界面顶部，点击 **"原创处理"** 按钮（红色按钮）。

### 3. 选择文件
- **输入视频**: 点击"浏览"选择要处理的视频文件
- **输出目录**: 点击"浏览"选择处理后视频的保存位置

### 4. 配置处理参数
使用四个选项卡配置不同的处理效果：

#### 🎨 画面处理
- **镜像翻转**: 水平/垂直翻转画面
- **色彩调整**: 调整亮度、对比度、饱和度（-100到+100）

#### 🎵 音频处理  
- **音频变速**: 调整播放速度（50%-200%）
- **音调调整**: 调整音调（±12半音）
- **背景音乐**: 添加背景音乐并调整音量
- **降噪处理**: 启用音频降噪

#### ⏱️ 时间轴处理
- **视频变速**: 调整整体播放速度（50%-200%）
- **随机剪切**: 启用随机剪切并设置次数（1-10次）
- **转场效果**: 添加转场动画效果

#### 📤 输出设置
- **格式**: 选择输出格式（MP4、AVI、MOV、MKV）
- **质量**: 选择输出质量（低/中/高/超高质量）
- **分辨率**: 选择输出分辨率（保持原始/720p/1080p/1440p/4K）

### 5. 使用快速预设
为了方便使用，提供了三个快速预设：

- **轻度处理**: 微调参数，保持原始质量
- **中度处理**: 适中修改，平衡效果和质量 ⭐ 推荐
- **重度处理**: 大幅修改，最大化处理效果

### 6. 开始处理
- 点击 **"开始处理"** 按钮开始视频处理
- 可以随时点击 **"停止处理"** 中断处理过程
- 处理进度会实时显示在进度条中

## 💡 使用技巧

### 参数调整建议
1. **首次使用**: 建议从"中度处理"预设开始
2. **质量优先**: 选择"高质量"输出设置
3. **效果平衡**: 避免过度处理影响观看体验
4. **测试验证**: 长视频建议先用短片段测试效果

### 最佳实践
1. **备份原文件**: 处理前务必备份原始视频
2. **分段测试**: 长视频建议先测试片段效果
3. **参数记录**: 记录有效的参数组合供后续使用
4. **质量检查**: 处理后检查输出质量是否满意

## ⚠️ 重要提醒

### 法律合规
- **本工具仅供学习和技术研究使用**
- **请确保您有权使用所处理的视频内容**
- **建议优先创作原创内容**
- **搬运他人原创内容可能涉及版权侵权**

### 技术要求
- 确保有足够的磁盘空间存储输出文件
- 处理大文件时需要较多内存和时间
- 建议在处理过程中不要运行其他占用资源的程序

## 🔧 故障排除

### 常见问题

**Q: 处理速度很慢怎么办？**
A: 可以降低输出质量或分辨率，减少同时应用的效果数量。

**Q: 输出文件很大怎么办？**
A: 调整输出质量设置为"中等质量"或"低质量"，选择合适的分辨率。

**Q: 某些效果不明显？**
A: 增加相应参数的数值，或组合多种效果使用。

**Q: 处理失败了？**
A: 检查输入文件是否完整，确保输出目录有写入权限，重启程序后重试。

**Q: 程序无响应？**
A: 可能是在处理大文件，请耐心等待。如果长时间无响应，可以重启程序。

### 技术支持
如遇到其他问题，请检查：
1. 输入文件格式是否支持
2. 输出目录是否存在且有写入权限
3. 系统资源是否充足
4. FFmpeg是否正常工作

## 📊 效果说明

### 检测抗性评估
- **轻度处理**: 中等检测抗性，适合质量敏感场景
- **中度处理**: 高检测抗性，推荐日常使用
- **重度处理**: 高检测抗性，最大化避免检测

### 处理效果
不同的参数组合会产生不同的处理效果，建议根据实际需求选择合适的设置。

---

**祝您使用愉快！记得合法合规使用工具。** 🎬✨
